<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <IncludeBuildOutput>false</IncludeBuildOutput>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>ArtDesignFramework.Analyzers</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>ArtDesignFramework Team</Authors>
    <Description>Roslyn analyzers for ArtDesignFramework architectural compliance checking</Description>
    <Copyright>Copyright © ArtDesignFramework 2025</Copyright>
    <PackageTags>roslyn;analyzer;artdesignframework;architecture;compliance</PackageTags>
    <NoPackageAnalysis>true</NoPackageAnalysis>
    <DevelopmentDependency>true</DevelopmentDependency>

    <!-- Analyzer-specific properties -->
    <EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules>
    <IsRoslynComponent>true</IsRoslynComponent>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.5.0" PrivateAssets="all" />
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" PrivateAssets="all" />
  </ItemGroup>

  <!-- Analyzer will be included in consuming projects, not self-referencing -->

  <ItemGroup>
    <None Include="tools\install.ps1" Pack="true" PackagePath="tools\install.ps1" />
    <None Include="tools\uninstall.ps1" Pack="true" PackagePath="tools\uninstall.ps1" />
  </ItemGroup>

</Project>
