// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Diagnostics;
using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
using ArtDesignFramework.Integration.Tests.Infrastructure;

using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace ArtDesignFramework.Integration.Tests;

/// <summary>
/// Performance benchmark tests for integration scenarios
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
public class PerformanceBenchmarkTests : IntegrationTestBase
{
    public PerformanceBenchmarkTests(ITestOutputHelper output) : base(output)
    {
    }

    /// <summary>
    /// Tests framework initialization performance
    /// </summary>
    [Fact]
    [TestableMethod("FrameworkInitializationPerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 3000)]
    public async Task FrameworkInitialization_ShouldMeetPerformanceTargets()
    {
        // Arrange
        Logger.LogInformation("Testing framework initialization performance");
        var stopwatch = Stopwatch.StartNew();

        // Act
        await ValidateFrameworkInitializationAsync();
        stopwatch.Stop();

        // Assert
        var initializationTime = stopwatch.ElapsedMilliseconds;
        Logger.LogInformation("Framework initialization completed in {InitTime}ms", initializationTime);

        initializationTime.Should().BeLessThan(3000, "Framework initialization should complete within 3 seconds");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("FrameworkInitializationPerformance", TimeSpan.FromSeconds(3));

        Logger.LogInformation("Framework initialization performance test completed successfully");
    }

    /// <summary>
    /// Tests service resolution performance
    /// </summary>
    [Fact]
    [TestableMethod("ServiceResolutionPerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task ServiceResolution_ShouldMeetPerformanceTargets()
    {
        // Arrange
        Logger.LogInformation("Testing service resolution performance");
        const int iterations = 1000;
        var serviceTypes = new[]
        {
            typeof(IFrameworkService),
            typeof(IAbilityRegistry),
            typeof(ICommandManager),
            typeof(ILayerManager)
        };

        // Act
        var stopwatch = Stopwatch.StartNew();

        for (int i = 0; i < iterations; i++)
        {
            foreach (var serviceType in serviceTypes)
            {
                var service = ServiceProvider.GetRequiredService(serviceType);
                service.Should().NotBeNull();
            }
        }

        stopwatch.Stop();

        // Assert
        var totalResolutions = iterations * serviceTypes.Length;
        var averageResolutionTime = stopwatch.ElapsedMilliseconds / (double)totalResolutions;

        Logger.LogInformation("Service resolution performance: {TotalResolutions} resolutions in {TotalTime}ms, " +
                             "average {AverageTime:F3}ms per resolution",
            totalResolutions, stopwatch.ElapsedMilliseconds, averageResolutionTime);

        averageResolutionTime.Should().BeLessThan(1.0, "Average service resolution should be under 1ms");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ServiceResolutionPerformance", TimeSpan.FromSeconds(2));

        Logger.LogInformation("Service resolution performance test completed successfully");
    }

    /// <summary>
    /// Tests ability registry performance
    /// </summary>
    [Fact]
    [TestableMethod("AbilityRegistryPerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task AbilityRegistry_ShouldMeetPerformanceTargets()
    {
        // Arrange
        Logger.LogInformation("Testing ability registry performance");
        await ValidateFrameworkInitializationAsync();

        var abilityRegistry = ServiceProvider.GetRequiredService<IAbilityRegistry>();
        const int iterations = 100;

        // Act - Test registration performance
        var registrationStopwatch = Stopwatch.StartNew();
        var testAbilities = new List<TestAbility>();

        for (int i = 0; i < iterations; i++)
        {
            var ability = new TestAbility($"PerfTest_{i}", $"Performance test ability {i}");
            testAbilities.Add(ability);
            await abilityRegistry.RegisterAbilityAsync(ability);
        }

        registrationStopwatch.Stop();

        // Act - Test retrieval performance
        var retrievalStopwatch = Stopwatch.StartNew();

        for (int i = 0; i < iterations; i++)
        {
            var abilities = abilityRegistry.GetRegisteredAbilities();
            abilities.Should().NotBeEmpty();
        }

        retrievalStopwatch.Stop();

        // Act - Test unregistration performance
        var unregistrationStopwatch = Stopwatch.StartNew();

        foreach (var ability in testAbilities)
        {
            await abilityRegistry.UnregisterAbilityAsync(ability.Name);
        }

        unregistrationStopwatch.Stop();

        // Assert
        var avgRegistrationTime = registrationStopwatch.ElapsedMilliseconds / (double)iterations;
        var avgRetrievalTime = retrievalStopwatch.ElapsedMilliseconds / (double)iterations;
        var avgUnregistrationTime = unregistrationStopwatch.ElapsedMilliseconds / (double)iterations;

        Logger.LogInformation("Ability registry performance - Registration: {RegTime:F2}ms avg, " +
                             "Retrieval: {RetTime:F2}ms avg, Unregistration: {UnregTime:F2}ms avg",
            avgRegistrationTime, avgRetrievalTime, avgUnregistrationTime);

        avgRegistrationTime.Should().BeLessThan(50, "Average ability registration should be under 50ms");
        avgRetrievalTime.Should().BeLessThan(10, "Average ability retrieval should be under 10ms");
        avgUnregistrationTime.Should().BeLessThan(50, "Average ability unregistration should be under 50ms");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("AbilityRegistryPerformance", TimeSpan.FromSeconds(2));

        Logger.LogInformation("Ability registry performance test completed successfully");
    }

    /// <summary>
    /// Tests concurrent operations performance
    /// </summary>
    [Fact]
    [TestableMethod("ConcurrentOperationsPerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public async Task ConcurrentOperations_ShouldMeetPerformanceTargets()
    {
        // Arrange
        Logger.LogInformation("Testing concurrent operations performance");
        await ValidateFrameworkInitializationAsync();

        const int concurrentTasks = 20;
        const int operationsPerTask = 50;

        // Act
        var stopwatch = Stopwatch.StartNew();

        var tasks = Enumerable.Range(0, concurrentTasks)
            .Select(async taskId =>
            {
                var taskStopwatch = Stopwatch.StartNew();

                for (int i = 0; i < operationsPerTask; i++)
                {
                    // Mix of different operations
                    var frameworkService = ServiceProvider.GetRequiredService<IFrameworkService>();
                    var abilityRegistry = ServiceProvider.GetRequiredService<IAbilityRegistry>();
                    var abilities = abilityRegistry.GetRegisteredAbilities();

                    // Simulate some work
                    await Task.Delay(1);
                }

                taskStopwatch.Stop();
                return taskStopwatch.ElapsedMilliseconds;
            });

        var taskTimes = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        var totalOperations = concurrentTasks * operationsPerTask;
        var averageTaskTime = taskTimes.Average();
        var maxTaskTime = taskTimes.Max();
        var operationsPerSecond = totalOperations / stopwatch.Elapsed.TotalSeconds;

        Logger.LogInformation("Concurrent operations performance - Total time: {TotalTime}ms, " +
                             "Average task time: {AvgTaskTime:F2}ms, Max task time: {MaxTaskTime}ms, " +
                             "Operations per second: {OpsPerSec:F0}",
            stopwatch.ElapsedMilliseconds, averageTaskTime, maxTaskTime, operationsPerSecond);

        averageTaskTime.Should().BeLessThan(2000, "Average task time should be under 2 seconds");
        maxTaskTime.Should().BeLessThan(3000, "Maximum task time should be under 3 seconds");
        operationsPerSecond.Should().BeGreaterThan(100, "Should handle at least 100 operations per second");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ConcurrentOperationsPerformance", TimeSpan.FromSeconds(5));

        Logger.LogInformation("Concurrent operations performance test completed successfully");
    }

    /// <summary>
    /// Tests memory usage during integration scenarios
    /// </summary>
    [Fact]
    [TestableMethod("MemoryUsagePerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 3000)]
    public async Task MemoryUsage_ShouldBeWithinAcceptableLimits()
    {
        // Arrange
        Logger.LogInformation("Testing memory usage performance");
        await ValidateFrameworkInitializationAsync();

        // Force garbage collection to get baseline
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var initialMemory = GC.GetTotalMemory(false);
        Logger.LogInformation("Initial memory usage: {InitialMemory:N0} bytes", initialMemory);

        // Act - Perform memory-intensive operations
        const int iterations = 1000;
        var services = new List<object>();

        for (int i = 0; i < iterations; i++)
        {
            // Create service instances
            services.Add(ServiceProvider.GetRequiredService<IFrameworkService>());
            services.Add(ServiceProvider.GetRequiredService<IAbilityRegistry>());
            services.Add(ServiceProvider.GetRequiredService<ICommandManager>());

            // Periodically check memory
            if (i % 100 == 0)
            {
                var currentMemory = GC.GetTotalMemory(false);
                Logger.LogDebug("Memory at iteration {Iteration}: {CurrentMemory:N0} bytes", i, currentMemory);
            }
        }

        // Force garbage collection
        services.Clear();
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        Logger.LogInformation("Memory usage - Initial: {InitialMemory:N0} bytes, " +
                             "Final: {FinalMemory:N0} bytes, Increase: {MemoryIncrease:N0} bytes",
            initialMemory, finalMemory, memoryIncrease);

        // Memory increase should be reasonable (less than 50MB for this test)
        memoryIncrease.Should().BeLessThan(50 * 1024 * 1024, "Memory increase should be under 50MB");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("MemoryUsagePerformance", TimeSpan.FromSeconds(3));

        Logger.LogInformation("Memory usage performance test completed successfully");
    }
}
