# ArtDesignFramework Troubleshooting Guide

Common architectural issues and their solutions for maintaining framework integrity and resolving development problems.

**Last Updated: 2025-01-27 22:50:00 UTC**

## Overview

This guide provides solutions to common architectural and development issues encountered when working with the ArtDesignFramework. It covers dependency problems, build failures, testing issues, and performance concerns.

## Dependency Issues

### Circular Dependency Detected

#### Symptoms
```
Error: Circular dependency detected: ModuleA → ModuleB → ModuleA
Build fails with circular reference errors
```

#### Root Causes
- Direct circular references between modules
- Indirect circular dependencies through multiple modules
- Shared types causing bidirectional dependencies

#### Solutions

**1. Extract Shared Abstractions**
```csharp
// ❌ Problem: Circular dependency
// ModuleA depends on ModuleB
// ModuleB depends on ModuleA

// ✅ Solution: Extract to Abstractions
// ModuleA depends on Abstractions
// ModuleB depends on Abstractions
// Abstractions has no dependencies
```

**2. Use Dependency Injection**
```csharp
// ❌ Problem: Direct instantiation
public class ServiceA
{
    private readonly ServiceB _serviceB = new ServiceB(); // Creates dependency
}

// ✅ Solution: Dependency injection
public class ServiceA
{
    private readonly IServiceB _serviceB;
    
    public ServiceA(IServiceB serviceB)
    {
        _serviceB = serviceB;
    }
}
```

**3. Event-Driven Communication**
```csharp
// ❌ Problem: Direct method calls
public class ModuleA
{
    public void ProcessData()
    {
        var moduleB = new ModuleB();
        moduleB.HandleData(data); // Creates dependency
    }
}

// ✅ Solution: Event-driven approach
public class ModuleA
{
    private readonly IEventBus _eventBus;
    
    public void ProcessData()
    {
        _eventBus.PublishAsync(new DataProcessedEvent(data));
    }
}
```

### Missing Project References

#### Symptoms
```
Error: The type or namespace 'TypeName' could not be found
Error: Project reference 'ProjectName' not found
```

#### Solutions

**1. Add Missing References**
```xml
<!-- Add to .csproj file -->
<ItemGroup>
  <ProjectReference Include="..\ArtDesignFramework.Abstractions\ArtDesignFramework.Abstractions.csproj" />
  <ProjectReference Include="..\ArtDesignFramework.Core\ArtDesignFramework.Core.csproj" />
</ItemGroup>
```

**2. Verify Reference Paths**
```bash
# Check if referenced projects exist
ls ../ArtDesignFramework.Abstractions/ArtDesignFramework.Abstractions.csproj
```

**3. Clean and Rebuild**
```bash
dotnet clean
dotnet restore
dotnet build
```

### Version Conflicts

#### Symptoms
```
Warning: Package downgrade detected
Error: Assembly version conflicts
```

#### Solutions

**1. Align Package Versions**
```xml
<!-- Use consistent versions across all projects -->
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
```

**2. Use Directory.Build.props**
```xml
<!-- Directory.Build.props in solution root -->
<Project>
  <PropertyGroup>
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
  </PropertyGroup>
</Project>
```

## Build Issues

### Compilation Failures

#### Symptoms
```
Error: CS0246: The type or namespace name could not be found
Error: CS0103: The name does not exist in the current context
```

#### Solutions

**1. Check Using Statements**
```csharp
// ✅ Add missing using statements
using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
```

**2. Verify Assembly References**
```csharp
// Check if types are in referenced assemblies
// Use Object Browser or Go to Definition
```

**3. Clean Build Environment**
```bash
# Clean all build artifacts
dotnet clean --configuration Debug
dotnet clean --configuration Release
rm -rf bin/ obj/
dotnet restore
dotnet build
```

### MSBuild Target Failures

#### Symptoms
```
Error: MSBuild target 'ValidateDependencies' failed
Error: Dependency validation failed
```

#### Solutions

**1. Check Dependency Rules**
- Review [DEPENDENCY_RULES.md](DEPENDENCY_RULES.md)
- Ensure dependencies follow layer hierarchy
- Remove forbidden references

**2. Update Validation Scripts**
```powershell
# Run dependency validation manually
.\tools\DependencyValidator.ps1 -ProjectPath "src/ModuleName"
```

**3. Disable Validation Temporarily**
```xml
<!-- Add to .csproj for debugging -->
<PropertyGroup>
  <SkipDependencyValidation>true</SkipDependencyValidation>
</PropertyGroup>
```

## Testing Issues

### Test Discovery Problems

#### Symptoms
```
No tests found in assembly
Test runner cannot discover tests
```

#### Solutions

**1. Check Test Framework References**
```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
<PackageReference Include="xunit" Version="2.6.2" />
<PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" />
```

**2. Verify Test Attributes**
```csharp
// ✅ Correct test method
[Fact]
[TestableMethod("TestMethodName", IncludeParameterValidation = true, IncludeExceptionTests = true)]
public void TestMethod()
{
    // Test implementation
}
```

**3. Check Test Project Configuration**
```xml
<PropertyGroup>
  <IsTestProject>true</IsTestProject>
  <IsPackable>false</IsPackable>
</PropertyGroup>
```

### Integration Test Failures

#### Symptoms
```
Service registration failures
Dependency injection errors in tests
```

#### Solutions

**1. Proper Service Setup**
```csharp
public class IntegrationTestBase : IDisposable
{
    protected readonly ServiceProvider ServiceProvider;

    protected IntegrationTestBase()
    {
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();
    }

    private void ConfigureServices(IServiceCollection services)
    {
        services.AddArtDesignFrameworkCore();
        services.AddArtDesignFrameworkTestFramework();
        // Add other required services
    }
}
```

**2. Mock External Dependencies**
```csharp
// Mock external services for testing
services.AddSingleton<IExternalService>(Mock.Of<IExternalService>());
```

## Performance Issues

### Slow Build Times

#### Symptoms
- Build takes longer than expected
- Frequent full rebuilds
- High CPU usage during build

#### Solutions

**1. Optimize Project References**
```xml
<!-- Use specific project references, not wildcards -->
<ProjectReference Include="..\Specific.Project\Specific.Project.csproj" />
```

**2. Enable Parallel Builds**
```bash
dotnet build --configuration Release --parallel
```

**3. Use Build Caching**
```xml
<PropertyGroup>
  <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
</PropertyGroup>
```

### Runtime Performance Issues

#### Symptoms
- Slow application startup
- High memory usage
- Poor response times

#### Solutions

**1. Optimize Service Registration**
```csharp
// ✅ Use appropriate service lifetimes
services.AddSingleton<IExpensiveService, ExpensiveService>();
services.AddScoped<IRequestScopedService, RequestScopedService>();
services.AddTransient<ILightweightService, LightweightService>();
```

**2. Implement Lazy Loading**
```csharp
public class ServiceWithLazyDependency
{
    private readonly Lazy<IExpensiveService> _expensiveService;
    
    public ServiceWithLazyDependency(Lazy<IExpensiveService> expensiveService)
    {
        _expensiveService = expensiveService;
    }
}
```

**3. Use Object Pooling**
```csharp
// For frequently allocated objects
services.AddSingleton<ObjectPool<StringBuilder>>(provider =>
{
    var policy = new StringBuilderPooledObjectPolicy();
    return new DefaultObjectPool<StringBuilder>(policy);
});
```

## Configuration Issues

### Missing Configuration

#### Symptoms
```
Configuration section 'SectionName' not found
Null reference exceptions for configuration values
```

#### Solutions

**1. Add Default Configuration**
```csharp
services.Configure<ModuleOptions>(options =>
{
    options.IsEnabled = true;
    options.TimeoutMs = 30000;
});
```

**2. Validate Configuration**
```csharp
services.AddOptions<ModuleOptions>()
    .Bind(configuration.GetSection("Module"))
    .ValidateDataAnnotations()
    .ValidateOnStart();
```

### Environment-Specific Issues

#### Symptoms
- Works in development but fails in production
- Different behavior across environments

#### Solutions

**1. Environment-Specific Configuration**
```json
// appsettings.Development.json
{
  "Module": {
    "IsEnabled": true,
    "DebugMode": true
  }
}

// appsettings.Production.json
{
  "Module": {
    "IsEnabled": true,
    "DebugMode": false
  }
}
```

**2. Configuration Validation**
```csharp
public class ModuleOptions
{
    [Required]
    public string ConnectionString { get; set; } = string.Empty;
    
    [Range(1000, 60000)]
    public int TimeoutMs { get; set; } = 30000;
}
```

## Debugging Strategies

### Dependency Injection Issues

**1. Service Registration Debugging**
```csharp
// Add logging to see service registrations
services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

// Log service registrations
foreach (var service in services)
{
    Console.WriteLine($"{service.ServiceType.Name} -> {service.ImplementationType?.Name}");
}
```

**2. Service Resolution Debugging**
```csharp
try
{
    var service = serviceProvider.GetRequiredService<IMyService>();
}
catch (InvalidOperationException ex)
{
    // Log the full exception to see what's missing
    logger.LogError(ex, "Service resolution failed");
    throw;
}
```

### Build Debugging

**1. Verbose Build Output**
```bash
dotnet build --verbosity diagnostic
```

**2. MSBuild Binary Log**
```bash
dotnet build -bl:build.binlog
# Open build.binlog in MSBuild Structured Log Viewer
```

## Prevention Strategies

### Automated Validation

**1. Pre-commit Hooks**
```bash
#!/bin/sh
# .git/hooks/pre-commit
dotnet build --no-restore
dotnet test --no-build --no-restore
```

**2. CI/CD Pipeline Checks**
```yaml
# Azure DevOps pipeline example
- task: DotNetCoreCLI@2
  displayName: 'Build'
  inputs:
    command: 'build'
    arguments: '--configuration Release --no-restore'

- task: DotNetCoreCLI@2
  displayName: 'Test'
  inputs:
    command: 'test'
    arguments: '--configuration Release --no-build --logger trx'
```

### Monitoring and Alerting

**1. Build Health Monitoring**
- Track build success rates
- Monitor build duration trends
- Alert on build failures

**2. Dependency Health Checks**
- Regular dependency audits
- Automated security scanning
- Version compatibility checks

## Getting Help

### Internal Resources
1. Check [DEPENDENCY_RULES.md](DEPENDENCY_RULES.md) for dependency guidelines
2. Review [MODULE_CONTRACTS.md](MODULE_CONTRACTS.md) for interface patterns
3. Consult [BEST_PRACTICES.md](BEST_PRACTICES.md) for coding standards
4. Check ADRs for architectural decisions

### External Resources
1. [.NET Documentation](https://docs.microsoft.com/en-us/dotnet/)
2. [MSBuild Documentation](https://docs.microsoft.com/en-us/visualstudio/msbuild/)
3. [xUnit Documentation](https://xunit.net/docs/getting-started/netcore/cmdline)
4. [Dependency Injection in .NET](https://docs.microsoft.com/en-us/dotnet/core/extensions/dependency-injection)

### Support Channels
- Architecture team for design questions
- Development team for implementation issues
- DevOps team for build and deployment issues
- QA team for testing and validation issues

---

**Note**: This troubleshooting guide is continuously updated based on common issues encountered by the development team. If you encounter a new issue, please document the solution and update this guide.
