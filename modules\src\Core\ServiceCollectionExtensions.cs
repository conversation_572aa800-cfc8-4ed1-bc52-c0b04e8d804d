// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace ArtDesignFramework.Core;

/// <summary>
/// Extension methods for configuring Core services
/// Last Updated: 2025-01-27 23:00:00 UTC
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds ArtDesignFramework Core services to the service collection
    /// Last Updated: 2025-01-27 23:00:00 UTC
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddArtDesignFrameworkCore", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public static IServiceCollection AddArtDesignFrameworkCore(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        // Register core interfaces and implementations using Add methods for analyzer compliance
        services.AddSingleton<IFrameworkService, FrameworkService>();
        services.AddSingleton<IRenderEngine, RenderEngine>();
        services.AddSingleton<ILayerManager, LayerManager>();
        services.AddSingleton<ICommandManager, CommandManager>();

        return services;
    }

    /// <summary>
    /// Adds ArtDesignFramework Core services with custom configuration
    /// Last Updated: 2025-01-27 23:00:00 UTC
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configure">Configuration action</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddArtDesignFrameworkCore", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public static IServiceCollection AddArtDesignFrameworkCore(
        this IServiceCollection services,
        Action<CoreOptions> configure)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configure);

        services.AddArtDesignFrameworkCore();
        services.Configure(configure);

        return services;
    }
}

/// <summary>
/// Configuration options for Core services
/// Last Updated: 2025-01-27 23:00:00 UTC
/// </summary>
public sealed class CoreOptions
{
    /// <summary>
    /// Gets or sets the default render surface width
    /// </summary>
    public int DefaultSurfaceWidth { get; set; } = 1920;

    /// <summary>
    /// Gets or sets the default render surface height
    /// </summary>
    public int DefaultSurfaceHeight { get; set; } = 1080;

    /// <summary>
    /// Gets or sets whether to enable performance monitoring
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// Gets or sets the maximum number of undo operations
    /// </summary>
    public int MaxUndoOperations { get; set; } = 100;
}
