// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.HealthMonitoring.Core;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ArtDesignFramework.HealthMonitoring.Services;

/// <summary>
/// Utility methods for AutomatedIntegrityMonitor
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public partial class AutomatedIntegrityMonitor
{
    /// <summary>
    /// Gets the current health status of a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>Current health information</returns>
    [TestableMethod("GetModuleHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<ModuleHealth?> GetModuleHealthAsync(string moduleName)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));

        await Task.CompletedTask; // Make async for interface compliance
        return _moduleHealthCache.GetValueOrDefault(moduleName);
    }

    /// <summary>
    /// Gets health history for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="timeRange">Time range for history</param>
    /// <returns>Historical health data</returns>
    [TestableMethod("GetHealthHistoryAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<IEnumerable<ModuleHealth>> GetHealthHistoryAsync(string moduleName, TimeSpan timeRange)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));

        await Task.CompletedTask; // Make async for interface compliance

        if (!_healthHistory.TryGetValue(moduleName, out var history))
            return Enumerable.Empty<ModuleHealth>();

        var cutoffTime = DateTime.UtcNow - timeRange;
        return history.Where(h => h.LastChecked >= cutoffTime).OrderBy(h => h.LastChecked);
    }

    /// <summary>
    /// Registers a custom health check for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="checkName">Health check name</param>
    /// <param name="healthCheck">Health check function</param>
    /// <returns>True if registration was successful</returns>
    [TestableMethod("RegisterHealthCheckAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<bool> RegisterHealthCheckAsync(
        string moduleName,
        string checkName,
        Func<CancellationToken, Task<HealthCheckResult>> healthCheck)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));
        if (string.IsNullOrWhiteSpace(checkName))
            throw new ArgumentException("Check name cannot be null or empty", nameof(checkName));
        if (healthCheck == null)
            throw new ArgumentNullException(nameof(healthCheck));

        await Task.CompletedTask; // Make async for interface compliance

        var moduleChecks = _customHealthChecks.GetOrAdd(moduleName, _ => new Dictionary<string, Func<CancellationToken, Task<HealthCheckResult>>>());
        moduleChecks[checkName] = healthCheck;

        _logger.LogInformation("Registered custom health check '{CheckName}' for module '{ModuleName}'", checkName, moduleName);
        return true;
    }

    /// <summary>
    /// Unregisters a health check
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="checkName">Health check name</param>
    /// <returns>True if unregistration was successful</returns>
    [TestableMethod("UnregisterHealthCheckAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<bool> UnregisterHealthCheckAsync(string moduleName, string checkName)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));
        if (string.IsNullOrWhiteSpace(checkName))
            throw new ArgumentException("Check name cannot be null or empty", nameof(checkName));

        await Task.CompletedTask; // Make async for interface compliance

        if (_customHealthChecks.TryGetValue(moduleName, out var moduleChecks))
        {
            var removed = moduleChecks.Remove(checkName);
            if (removed)
            {
                _logger.LogInformation("Unregistered custom health check '{CheckName}' for module '{ModuleName}'", checkName, moduleName);
            }
            return removed;
        }

        return false;
    }

    /// <summary>
    /// Gets active health alerts
    /// </summary>
    /// <param name="moduleName">Optional module name filter</param>
    /// <returns>Active alerts</returns>
    [TestableMethod("GetActiveAlertsAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<IEnumerable<HealthAlert>> GetActiveAlertsAsync(string? moduleName = null)
    {
        await Task.CompletedTask; // Make async for interface compliance

        var alerts = _activeAlerts.Values.Where(a => a.IsActive);
        
        if (!string.IsNullOrWhiteSpace(moduleName))
        {
            alerts = alerts.Where(a => a.ModuleName.Equals(moduleName, StringComparison.OrdinalIgnoreCase));
        }

        return alerts.OrderByDescending(a => a.Timestamp);
    }

    /// <summary>
    /// Acknowledges a health alert
    /// </summary>
    /// <param name="alertId">Alert ID</param>
    /// <param name="acknowledgedBy">Who acknowledged the alert</param>
    /// <returns>True if acknowledgment was successful</returns>
    [TestableMethod("AcknowledgeAlertAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<bool> AcknowledgeAlertAsync(string alertId, string acknowledgedBy)
    {
        if (string.IsNullOrWhiteSpace(alertId))
            throw new ArgumentException("Alert ID cannot be null or empty", nameof(alertId));
        if (string.IsNullOrWhiteSpace(acknowledgedBy))
            throw new ArgumentException("Acknowledged by cannot be null or empty", nameof(acknowledgedBy));

        await Task.CompletedTask; // Make async for interface compliance

        if (_activeAlerts.TryGetValue(alertId, out var alert))
        {
            alert.AcknowledgedAt = DateTime.UtcNow;
            alert.AcknowledgedBy = acknowledgedBy;
            
            _logger.LogInformation("Alert {AlertId} acknowledged by {AcknowledgedBy}", alertId, acknowledgedBy);
            return true;
        }

        return false;
    }

    /// <summary>
    /// Analyzes health trends for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="metricName">Metric name</param>
    /// <param name="timeRange">Time range for analysis</param>
    /// <returns>Health trend analysis</returns>
    [TestableMethod("AnalyzeHealthTrendAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<HealthTrend> AnalyzeHealthTrendAsync(string moduleName, string metricName, TimeSpan timeRange)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));
        if (string.IsNullOrWhiteSpace(metricName))
            throw new ArgumentException("Metric name cannot be null or empty", nameof(metricName));

        await Task.CompletedTask; // Make async for interface compliance

        var history = await GetHealthHistoryAsync(moduleName, timeRange);
        var dataPoints = new List<(DateTime Timestamp, double Value)>();

        foreach (var health in history)
        {
            if (health.Metrics.TryGetValue(metricName, out var value) && value is double doubleValue)
            {
                dataPoints.Add((health.LastChecked, doubleValue));
            }
        }

        var trend = new HealthTrend
        {
            ModuleName = moduleName,
            MetricName = metricName,
            DataPoints = dataPoints
        };

        if (dataPoints.Count >= 2)
        {
            // Simple linear trend analysis
            var firstValue = dataPoints.First().Value;
            var lastValue = dataPoints.Last().Value;
            var change = lastValue - firstValue;
            var changePercent = Math.Abs(change / firstValue);

            trend.Direction = change > 0 ? TrendDirection.Improving : 
                             change < 0 ? TrendDirection.Degrading : TrendDirection.Stable;
            trend.Strength = Math.Min(changePercent, 1.0);
            trend.Confidence = Math.Min(dataPoints.Count / 10.0, 1.0); // More data points = higher confidence
            trend.PredictedValue = lastValue + (change * 0.1); // Simple prediction
            trend.PredictionTimeframe = TimeSpan.FromHours(1);

            // Add recommendations based on trend
            if (trend.Direction == TrendDirection.Degrading && trend.Strength > 0.1)
            {
                trend.Recommendations.Add($"Monitor {metricName} closely - degrading trend detected");
                if (trend.Strength > 0.3)
                {
                    trend.Recommendations.Add($"Consider investigating root cause of {metricName} degradation");
                }
            }
        }

        return trend;
    }

    /// <summary>
    /// Gets predictive health insights
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="predictionHorizon">How far ahead to predict</param>
    /// <returns>Predictive insights</returns>
    [TestableMethod("GetPredictiveInsightsAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<IEnumerable<HealthTrend>> GetPredictiveInsightsAsync(string moduleName, TimeSpan predictionHorizon)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));

        var insights = new List<HealthTrend>();
        var commonMetrics = new[] { "HealthScore", "ResponseTime", "ErrorRate", "MemoryUsage" };

        foreach (var metric in commonMetrics)
        {
            var trend = await AnalyzeHealthTrendAsync(moduleName, metric, predictionHorizon);
            if (trend.DataPoints.Count > 0)
            {
                insights.Add(trend);
            }
        }

        return insights;
    }

    /// <summary>
    /// Attempts automatic remediation for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="issue">Issue to remediate</param>
    /// <returns>True if remediation was attempted</returns>
    [TestableMethod("AttemptRemediationAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<bool> AttemptRemediationAsync(string moduleName, string issue)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));
        if (string.IsNullOrWhiteSpace(issue))
            throw new ArgumentException("Issue cannot be null or empty", nameof(issue));

        if (!_options.EnableAutoRemediation)
        {
            _logger.LogInformation("Auto-remediation is disabled, skipping remediation for {ModuleName}: {Issue}", moduleName, issue);
            return false;
        }

        _logger.LogInformation("Attempting auto-remediation for {ModuleName}: {Issue}", moduleName, issue);

        try
        {
            // Basic remediation strategies
            switch (issue.ToLowerInvariant())
            {
                case "compilation":
                    return await AttemptCompilationFixAsync(moduleName);
                case "dependencies":
                    return await AttemptDependencyFixAsync(moduleName);
                case "performance":
                    return await AttemptPerformanceFixAsync(moduleName);
                default:
                    _logger.LogWarning("No remediation strategy available for issue: {Issue}", issue);
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during auto-remediation for {ModuleName}: {Issue}", moduleName, issue);
            return false;
        }
    }

    /// <summary>
    /// Gets overall system health summary
    /// </summary>
    /// <returns>System health summary</returns>
    [TestableMethod("GetSystemHealthSummaryAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<Dictionary<string, object>> GetSystemHealthSummaryAsync()
    {
        await Task.CompletedTask; // Make async for interface compliance

        var allHealth = _moduleHealthCache.Values.ToList();
        var summary = new Dictionary<string, object>
        {
            ["TotalModules"] = allHealth.Count,
            ["HealthyModules"] = allHealth.Count(h => h.Status == ModuleHealthStatus.Healthy),
            ["DegradedModules"] = allHealth.Count(h => h.Status == ModuleHealthStatus.Degraded),
            ["UnhealthyModules"] = allHealth.Count(h => h.Status == ModuleHealthStatus.Unhealthy),
            ["CriticalModules"] = allHealth.Count(h => h.Status == ModuleHealthStatus.Critical),
            ["AverageHealthScore"] = allHealth.Count > 0 ? allHealth.Average(h => h.HealthScore) : 0.0,
            ["ActiveAlerts"] = _activeAlerts.Values.Count(a => a.IsActive),
            ["LastUpdateTime"] = DateTime.UtcNow,
            ["MonitoringStatus"] = _isMonitoring ? "Active" : "Inactive"
        };

        // Calculate overall system health score (target: 90%+)
        var overallScore = allHealth.Count > 0 ? allHealth.Average(h => h.HealthScore) : 0.0;
        summary["OverallHealthScore"] = overallScore;
        summary["MeetsIntegrityTarget"] = overallScore >= 0.9; // 90% target

        return summary;
    }

    /// <summary>
    /// Exports health data for analysis
    /// </summary>
    /// <param name="format">Export format (json, csv, xml)</param>
    /// <param name="timeRange">Time range for export</param>
    /// <param name="moduleName">Optional module filter</param>
    /// <returns>Exported data</returns>
    [TestableMethod("ExportHealthDataAsync", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public async Task<string> ExportHealthDataAsync(string format, TimeSpan timeRange, string? moduleName = null)
    {
        if (string.IsNullOrWhiteSpace(format))
            throw new ArgumentException("Format cannot be null or empty", nameof(format));

        var exportData = new List<object>();
        var modules = string.IsNullOrWhiteSpace(moduleName) ? 
                     _moduleHealthCache.Keys : 
                     new[] { moduleName };

        foreach (var module in modules)
        {
            var history = await GetHealthHistoryAsync(module, timeRange);
            exportData.AddRange(history);
        }

        return format.ToLowerInvariant() switch
        {
            "json" => JsonSerializer.Serialize(exportData, new JsonSerializerOptions { WriteIndented = true }),
            "csv" => ConvertToCsv(exportData),
            "xml" => ConvertToXml(exportData),
            _ => throw new ArgumentException($"Unsupported export format: {format}", nameof(format))
        };
    }
}
