// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.HealthMonitoring.Core;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;

namespace ArtDesignFramework.HealthMonitoring.Services;

/// <summary>
/// Helper methods for AutomatedIntegrityMonitor
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public partial class AutomatedIntegrityMonitor
{
    /// <summary>
    /// Gets the project path for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>Project file path</returns>
    private string GetModuleProjectPath(string moduleName)
    {
        // Standard module structure: modules/src/{ModuleName}/ArtDesignFramework.{ModuleName}.csproj
        var basePath = Path.Combine("modules", "src", moduleName);
        var projectFile = $"ArtDesignFramework.{moduleName}.csproj";
        var fullPath = Path.Combine(basePath, projectFile);

        if (File.Exists(fullPath))
            return fullPath;

        // Try alternative naming patterns
        var alternatives = new[]
        {
            Path.Combine(basePath, $"{moduleName}.csproj"),
            Path.Combine(basePath, $"ArtDesignFramework.{moduleName}.csproj"),
            Path.Combine("modules", "production", "src", moduleName, $"ArtDesignFramework.{moduleName}.csproj")
        };

        foreach (var alternative in alternatives)
        {
            if (File.Exists(alternative))
                return alternative;
        }

        _logger.LogWarning("Project file not found for module: {ModuleName}", moduleName);
        return string.Empty;
    }

    /// <summary>
    /// Gets the version of a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>Module version</returns>
    private string GetModuleVersion(string moduleName)
    {
        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath) || !File.Exists(projectPath))
                return "Unknown";

            var projectContent = File.ReadAllText(projectPath);
            var versionMatch = Regex.Match(projectContent, @"<Version>([^<]+)</Version>");
            
            return versionMatch.Success ? versionMatch.Groups[1].Value : "1.0.0";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get version for module: {ModuleName}", moduleName);
            return "Unknown";
        }
    }

    /// <summary>
    /// Counts analyzer issues in build output
    /// </summary>
    /// <param name="buildOutput">Build output text</param>
    /// <param name="analyzerPrefix">Analyzer prefix (e.g., "ADF")</param>
    /// <returns>Number of issues found</returns>
    private int CountAnalyzerIssues(string buildOutput, string analyzerPrefix)
    {
        if (string.IsNullOrEmpty(buildOutput))
            return 0;

        var pattern = $@"{analyzerPrefix}\d+";
        var matches = Regex.Matches(buildOutput, pattern, RegexOptions.IgnoreCase);
        return matches.Count;
    }

    /// <summary>
    /// Checks if module has performance tests
    /// </summary>
    /// <param name="projectPath">Project file path</param>
    /// <returns>True if performance tests exist</returns>
    private bool HasPerformanceTests(string? projectPath)
    {
        if (string.IsNullOrEmpty(projectPath))
            return false;

        var projectDir = Path.GetDirectoryName(projectPath);
        if (string.IsNullOrEmpty(projectDir))
            return false;

        // Look for performance test files
        var performanceTestPatterns = new[]
        {
            "*Performance*.cs",
            "*Benchmark*.cs",
            "*Load*.cs"
        };

        foreach (var pattern in performanceTestPatterns)
        {
            var files = Directory.GetFiles(projectDir, pattern, SearchOption.AllDirectories);
            if (files.Length > 0)
                return true;
        }

        return false;
    }

    /// <summary>
    /// Checks if module has ServiceCollectionExtensions
    /// </summary>
    /// <param name="projectPath">Project file path</param>
    /// <returns>True if ServiceCollectionExtensions exists</returns>
    private bool HasServiceCollectionExtensions(string? projectPath)
    {
        if (string.IsNullOrEmpty(projectPath))
            return false;

        var projectDir = Path.GetDirectoryName(projectPath);
        if (string.IsNullOrEmpty(projectDir))
            return false;

        var extensionsFile = Path.Combine(projectDir, "ServiceCollectionExtensions.cs");
        return File.Exists(extensionsFile);
    }

    /// <summary>
    /// Calculates overall health score for a module
    /// </summary>
    /// <param name="moduleHealth">Module health information</param>
    private void CalculateHealthScore(ModuleHealth moduleHealth)
    {
        if (moduleHealth.CheckResults.Count == 0)
        {
            moduleHealth.HealthScore = 0.0;
            moduleHealth.Status = ModuleHealthStatus.Unknown;
            return;
        }

        var totalChecks = moduleHealth.CheckResults.Count;
        var healthyChecks = moduleHealth.CheckResults.Count(r => r.IsHealthy);
        var criticalFailures = moduleHealth.CheckResults.Count(r => !r.IsHealthy && r.Severity == HealthCheckSeverity.Critical);
        var errorFailures = moduleHealth.CheckResults.Count(r => !r.IsHealthy && r.Severity == HealthCheckSeverity.Error);

        // Calculate base score
        var baseScore = (double)healthyChecks / totalChecks;

        // Apply penalties for severity
        var criticalPenalty = criticalFailures * 0.3;
        var errorPenalty = errorFailures * 0.2;

        moduleHealth.HealthScore = Math.Max(0.0, baseScore - criticalPenalty - errorPenalty);

        // Determine status based on score and failures
        if (criticalFailures > 0)
        {
            moduleHealth.Status = ModuleHealthStatus.Critical;
        }
        else if (moduleHealth.HealthScore >= 0.8)
        {
            moduleHealth.Status = ModuleHealthStatus.Healthy;
        }
        else if (moduleHealth.HealthScore >= 0.6)
        {
            moduleHealth.Status = ModuleHealthStatus.Degraded;
        }
        else
        {
            moduleHealth.Status = ModuleHealthStatus.Unhealthy;
        }

        // Collect errors and warnings
        moduleHealth.Errors.Clear();
        moduleHealth.Warnings.Clear();

        foreach (var result in moduleHealth.CheckResults.Where(r => !r.IsHealthy))
        {
            if (result.Severity == HealthCheckSeverity.Critical || result.Severity == HealthCheckSeverity.Error)
            {
                moduleHealth.Errors.Add($"{result.CheckName}: {result.Message}");
            }
            else
            {
                moduleHealth.Warnings.Add($"{result.CheckName}: {result.Message}");
            }
        }
    }

    /// <summary>
    /// Updates health history for a module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="health">Health information</param>
    private void UpdateHealthHistory(string moduleName, ModuleHealth health)
    {
        var history = _healthHistory.GetOrAdd(moduleName, _ => new List<ModuleHealth>());
        
        lock (history)
        {
            history.Add(health);
            
            // Keep only recent history within retention period
            var cutoffTime = DateTime.UtcNow - _options.HealthRecordRetention;
            history.RemoveAll(h => h.LastChecked < cutoffTime);
            
            // Also limit by max records
            if (history.Count > _options.MaxHealthRecords)
            {
                var excess = history.Count - _options.MaxHealthRecords;
                history.RemoveRange(0, excess);
            }
        }
    }

    /// <summary>
    /// Processes alerts for a module
    /// </summary>
    /// <param name="moduleHealth">Module health information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the alert processing</returns>
    private async Task ProcessModuleAlertsAsync(ModuleHealth moduleHealth, CancellationToken cancellationToken)
    {
        await Task.CompletedTask; // Make async for consistency

        // Check health score threshold
        if (_options.AlertThresholds.TryGetValue("HealthScore", out var healthThreshold) && 
            moduleHealth.HealthScore < healthThreshold)
        {
            await CreateAlertAsync(moduleHealth.ModuleName, HealthCheckSeverity.Warning, 
                "Health Score Below Threshold", 
                $"Module health score ({moduleHealth.HealthScore:F2}) is below threshold ({healthThreshold:F2})",
                "HealthScore", moduleHealth.HealthScore, healthThreshold);
        }

        // Check for critical failures
        var criticalFailures = moduleHealth.CheckResults.Where(r => !r.IsHealthy && r.Severity == HealthCheckSeverity.Critical);
        foreach (var failure in criticalFailures)
        {
            await CreateAlertAsync(moduleHealth.ModuleName, HealthCheckSeverity.Critical,
                $"Critical Failure: {failure.CheckName}",
                failure.Message,
                failure.CheckName, null, null);
        }

        // Check response time if available
        var avgResponseTime = moduleHealth.CheckResults.Average(r => r.Duration.TotalMilliseconds);
        if (_options.AlertThresholds.TryGetValue("ResponseTime", out var responseThreshold) && 
            avgResponseTime > responseThreshold)
        {
            await CreateAlertAsync(moduleHealth.ModuleName, HealthCheckSeverity.Warning,
                "High Response Time",
                $"Average response time ({avgResponseTime:F0}ms) exceeds threshold ({responseThreshold:F0}ms)",
                "ResponseTime", avgResponseTime, responseThreshold);
        }
    }

    /// <summary>
    /// Creates a health alert
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="severity">Alert severity</param>
    /// <param name="message">Alert message</param>
    /// <param name="description">Alert description</param>
    /// <param name="triggerMetric">Metric that triggered the alert</param>
    /// <param name="currentValue">Current value</param>
    /// <param name="thresholdValue">Threshold value</param>
    /// <returns>Task representing the alert creation</returns>
    private async Task CreateAlertAsync(string moduleName, HealthCheckSeverity severity, string message, 
        string description, string triggerMetric, object? currentValue, object? thresholdValue)
    {
        await Task.CompletedTask; // Make async for consistency

        var alert = new HealthAlert
        {
            ModuleName = moduleName,
            Severity = severity,
            Message = message,
            Description = description,
            TriggerMetric = triggerMetric,
            CurrentValue = currentValue,
            ThresholdValue = thresholdValue,
            Timestamp = DateTime.UtcNow
        };

        _activeAlerts.TryAdd(alert.Id, alert);
        
        _logger.LogWarning("Health alert created for {ModuleName}: {Message}", moduleName, message);
        AlertTriggered?.Invoke(this, alert);
    }

    /// <summary>
    /// Checks if an alert should be maintained
    /// </summary>
    /// <param name="alert">Alert to check</param>
    /// <returns>True if alert should remain active</returns>
    private async Task<bool> ShouldMaintainAlertAsync(HealthAlert alert)
    {
        await Task.CompletedTask; // Make async for consistency

        // Get current module health
        var currentHealth = _moduleHealthCache.GetValueOrDefault(alert.ModuleName);
        if (currentHealth == null)
            return true; // Keep alert if we can't check current status

        // Check if the condition that triggered the alert still exists
        switch (alert.TriggerMetric)
        {
            case "HealthScore":
                if (_options.AlertThresholds.TryGetValue("HealthScore", out var threshold))
                {
                    return currentHealth.HealthScore < threshold;
                }
                break;

            case "ResponseTime":
                var avgResponseTime = currentHealth.CheckResults.Average(r => r.Duration.TotalMilliseconds);
                if (_options.AlertThresholds.TryGetValue("ResponseTime", out var responseThreshold))
                {
                    return avgResponseTime > responseThreshold;
                }
                break;

            default:
                // For specific check failures, check if the check is still failing
                var failingCheck = currentHealth.CheckResults.FirstOrDefault(r => 
                    r.CheckName == alert.TriggerMetric && !r.IsHealthy && r.Severity == alert.Severity);
                return failingCheck != null;
        }

        return false;
    }

    /// <summary>
    /// Attempts to fix compilation issues
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>True if fix was attempted</returns>
    private async Task<bool> AttemptCompilationFixAsync(string moduleName)
    {
        _logger.LogInformation("Attempting compilation fix for module: {ModuleName}", moduleName);
        
        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath))
                return false;

            // Try dotnet restore first
            var restoreProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"restore \"{projectPath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            restoreProcess.Start();
            await restoreProcess.WaitForExitAsync();

            _logger.LogInformation("Restore completed for {ModuleName} with exit code: {ExitCode}", 
                moduleName, restoreProcess.ExitCode);

            return restoreProcess.ExitCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during compilation fix for module: {ModuleName}", moduleName);
            return false;
        }
    }

    /// <summary>
    /// Attempts to fix dependency issues
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>True if fix was attempted</returns>
    private async Task<bool> AttemptDependencyFixAsync(string moduleName)
    {
        _logger.LogInformation("Attempting dependency fix for module: {ModuleName}", moduleName);
        
        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath))
                return false;

            // Run dotnet restore with force
            var restoreProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"restore \"{projectPath}\" --force",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            restoreProcess.Start();
            await restoreProcess.WaitForExitAsync();

            return restoreProcess.ExitCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during dependency fix for module: {ModuleName}", moduleName);
            return false;
        }
    }

    /// <summary>
    /// Attempts to fix performance issues
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <returns>True if fix was attempted</returns>
    private async Task<bool> AttemptPerformanceFixAsync(string moduleName)
    {
        _logger.LogInformation("Attempting performance fix for module: {ModuleName}", moduleName);
        
        try
        {
            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            await Task.Delay(1000); // Give system time to stabilize

            _logger.LogInformation("Performance fix attempted for module: {ModuleName}", moduleName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during performance fix for module: {ModuleName}", moduleName);
            return false;
        }
    }

    /// <summary>
    /// Converts data to CSV format
    /// </summary>
    /// <param name="data">Data to convert</param>
    /// <returns>CSV string</returns>
    private string ConvertToCsv(IEnumerable<object> data)
    {
        var csv = new StringBuilder();
        csv.AppendLine("ModuleName,Version,Status,HealthScore,LastChecked,CheckDuration,Errors,Warnings");

        foreach (var item in data.OfType<ModuleHealth>())
        {
            csv.AppendLine($"{item.ModuleName},{item.Version},{item.Status},{item.HealthScore:F2}," +
                          $"{item.LastChecked:yyyy-MM-dd HH:mm:ss},{item.CheckDuration.TotalMilliseconds:F0}," +
                          $"\"{string.Join("; ", item.Errors)}\",\"{string.Join("; ", item.Warnings)}\"");
        }

        return csv.ToString();
    }

    /// <summary>
    /// Converts data to XML format
    /// </summary>
    /// <param name="data">Data to convert</param>
    /// <returns>XML string</returns>
    private string ConvertToXml(IEnumerable<object> data)
    {
        var root = new XElement("HealthData");

        foreach (var item in data.OfType<ModuleHealth>())
        {
            var moduleElement = new XElement("Module",
                new XAttribute("Name", item.ModuleName),
                new XAttribute("Version", item.Version),
                new XAttribute("Status", item.Status),
                new XAttribute("HealthScore", item.HealthScore.ToString("F2")),
                new XAttribute("LastChecked", item.LastChecked.ToString("yyyy-MM-dd HH:mm:ss")),
                new XAttribute("CheckDuration", item.CheckDuration.TotalMilliseconds.ToString("F0")));

            if (item.Errors.Count > 0)
            {
                var errorsElement = new XElement("Errors");
                foreach (var error in item.Errors)
                {
                    errorsElement.Add(new XElement("Error", error));
                }
                moduleElement.Add(errorsElement);
            }

            if (item.Warnings.Count > 0)
            {
                var warningsElement = new XElement("Warnings");
                foreach (var warning in item.Warnings)
                {
                    warningsElement.Add(new XElement("Warning", warning));
                }
                moduleElement.Add(warningsElement);
            }

            root.Add(moduleElement);
        }

        return root.ToString();
    }
}
