// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
using ArtDesignFramework.VectorGraphics.Core.Interfaces;
using ArtDesignFramework.VectorGraphics.Core.Models;
using ArtDesignFramework.VectorGraphics.Operations;
using ArtDesignFramework.VectorGraphics.Rendering;
using ArtDesignFramework.VectorGraphics.Svg;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.VectorGraphics;

/// <summary>
/// Main engine for vector graphics operations, providing a unified interface
/// </summary>
[Testable]
public sealed class VectorGraphicsEngine : IDisposable
{
    private readonly ILogger<VectorGraphicsEngine>? _logger;
    private readonly IVectorRenderer _renderer;
    private readonly IPathOperations _pathOperations;
    private readonly ISvgProcessor _svgProcessor;
    private bool _isInitialized;
    private bool _disposed;

    /// <summary>
    /// Gets whether the engine is initialized
    /// </summary>
    public bool IsInitialized => _isInitialized;

    /// <summary>
    /// Gets the vector renderer
    /// </summary>
    public IVectorRenderer Renderer => _renderer;

    /// <summary>
    /// Gets the path operations processor
    /// </summary>
    public IPathOperations PathOperations => _pathOperations;

    /// <summary>
    /// Gets the SVG processor
    /// </summary>
    public ISvgProcessor SvgProcessor => _svgProcessor;

    /// <summary>
    /// Gets performance metrics for the last operation
    /// </summary>
    public VectorRenderMetrics? LastMetrics { get; private set; }

    /// <summary>
    /// Initializes a new instance of the VectorGraphicsEngine class
    /// </summary>
    /// <param name="logger">Optional logger instance</param>
    public VectorGraphicsEngine(ILogger<VectorGraphicsEngine>? logger = null)
    {
        _logger = logger;
        _renderer = new VectorRenderer();
        _pathOperations = new PathOperations();
        _svgProcessor = new SvgProcessor();

        _logger?.LogInformation("VectorGraphicsEngine created");
    }

    /// <summary>
    /// Initializes the vector graphics engine
    /// </summary>
    /// <param name="config">Optional renderer configuration</param>
    /// <returns>Task representing the initialization operation</returns>
    public async Task InitializeAsync(VectorRendererConfig? config = null)
    {
        if (_isInitialized)
            return;

        ThrowIfDisposed();

        try
        {
            _logger?.LogInformation("Initializing VectorGraphicsEngine...");

            config ??= VectorRendererConfig.Default();
            await _renderer.InitializeAsync(config);

            _isInitialized = true;

            _logger?.LogInformation("VectorGraphicsEngine initialized successfully");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to initialize VectorGraphicsEngine");
            throw;
        }
    }

    /// <summary>
    /// Creates a new vector document
    /// </summary>
    /// <param name="bounds">The document bounds</param>
    /// <returns>A new vector document</returns>
    public VectorDocument CreateDocument(VectorBounds bounds)
    {
        ThrowIfDisposed();
        ThrowIfNotInitialized();

        var document = new VectorDocument(bounds);
        _logger?.LogDebug("Created new vector document with bounds {Bounds}", bounds);

        return document;
    }

    /// <summary>
    /// Creates a new vector document with default bounds
    /// </summary>
    /// <returns>A new vector document</returns>
    public VectorDocument CreateDocument()
    {
        return CreateDocument(new VectorBounds(0, 0, 800, 600));
    }

    /// <summary>
    /// Renders a vector document to a stream
    /// </summary>
    /// <param name="document">The document to render</param>
    /// <param name="stream">The stream to write to</param>
    /// <param name="format">The export format</param>
    /// <param name="renderContext">Optional render context</param>
    /// <param name="quality">The quality setting (0-100)</param>
    /// <returns>Task representing the render operation</returns>
    public async Task RenderToStreamAsync(
        VectorDocument document,
        Stream stream,
        Core.Enums.ExportFormat format,
        VectorRenderContext? renderContext = null,
        int quality = 90)
    {
        ThrowIfDisposed();
        ThrowIfNotInitialized();
        ArgumentNullException.ThrowIfNull(document);
        ArgumentNullException.ThrowIfNull(stream);

        try
        {
            _logger?.LogDebug("Rendering document to {Format} format", format);

            var startTime = DateTime.UtcNow;

            if (format == Core.Enums.ExportFormat.Svg)
            {
                await _svgProcessor.ExportToStreamAsync(document, stream);
            }
            else
            {
                renderContext ??= VectorRenderContext.Default((int)document.Bounds.Width, (int)document.Bounds.Height);

                using var surface = _renderer.CreateSurface((int)document.Bounds.Width, (int)document.Bounds.Height);
                _renderer.BeginRender(surface, renderContext);
                _renderer.RenderDocument(document);
                await _renderer.ExportAsync(format, stream, quality);
                _renderer.EndRender();
            }

            var renderTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            LastMetrics = new VectorRenderMetrics
            {
                RenderTimeMs = renderTime,
                ShapesRendered = document.ShapeCount
            };

            _logger?.LogDebug("Document rendered successfully in {RenderTime}ms", renderTime);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to render document");
            throw;
        }
    }

    /// <summary>
    /// Renders a vector document to a file
    /// </summary>
    /// <param name="document">The document to render</param>
    /// <param name="filePath">The file path to write to</param>
    /// <param name="format">The export format</param>
    /// <param name="renderContext">Optional render context</param>
    /// <param name="quality">The quality setting (0-100)</param>
    /// <returns>Task representing the render operation</returns>
    public async Task RenderToFileAsync(
        VectorDocument document,
        string filePath,
        Core.Enums.ExportFormat format,
        VectorRenderContext? renderContext = null,
        int quality = 90)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(filePath);

        using var fileStream = File.Create(filePath);
        await RenderToStreamAsync(document, fileStream, format, renderContext, quality);
    }

    /// <summary>
    /// Loads a vector document from an SVG file
    /// </summary>
    /// <param name="filePath">The path to the SVG file</param>
    /// <returns>The loaded vector document</returns>
    public async Task<VectorDocument> LoadFromSvgFileAsync(string filePath)
    {
        ThrowIfDisposed();
        ThrowIfNotInitialized();
        ArgumentNullException.ThrowIfNull(filePath);

        try
        {
            _logger?.LogDebug("Loading document from SVG file: {FilePath}", filePath);

            var document = await _svgProcessor.ImportFromFileAsync(filePath);

            _logger?.LogDebug("Document loaded successfully with {ShapeCount} shapes", document.ShapeCount);

            return document;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to load document from SVG file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Loads a vector document from an SVG stream
    /// </summary>
    /// <param name="stream">The stream containing SVG data</param>
    /// <returns>The loaded vector document</returns>
    public async Task<VectorDocument> LoadFromSvgStreamAsync(Stream stream)
    {
        ThrowIfDisposed();
        ThrowIfNotInitialized();
        ArgumentNullException.ThrowIfNull(stream);

        try
        {
            _logger?.LogDebug("Loading document from SVG stream");

            var document = await _svgProcessor.ImportFromStreamAsync(stream);

            _logger?.LogDebug("Document loaded successfully with {ShapeCount} shapes", document.ShapeCount);

            return document;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to load document from SVG stream");
            throw;
        }
    }

    /// <summary>
    /// Gets performance metrics for the engine
    /// </summary>
    /// <returns>Performance metrics</returns>
    public VectorRenderMetrics GetPerformanceMetrics()
    {
        ThrowIfDisposed();
        return LastMetrics ?? VectorRenderMetrics.Empty();
    }

    /// <summary>
    /// Validates the engine state
    /// </summary>
    /// <returns>True if the engine is in a valid state</returns>
    public bool Validate()
    {
        ThrowIfDisposed();
        return _isInitialized && _renderer.IsInitialized;
    }

    /// <summary>
    /// Shuts down the vector graphics engine
    /// </summary>
    public void Shutdown()
    {
        if (_disposed)
            return;

        try
        {
            _logger?.LogInformation("Shutting down VectorGraphicsEngine...");

            _isInitialized = false;

            _logger?.LogInformation("VectorGraphicsEngine shutdown completed");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during VectorGraphicsEngine shutdown");
        }
    }

    /// <summary>
    /// Disposes the vector graphics engine
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            Shutdown();
            _renderer?.Dispose();
            _pathOperations?.Dispose();
            _svgProcessor?.Dispose();
            _disposed = true;
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(VectorGraphicsEngine));
    }

    private void ThrowIfNotInitialized()
    {
        if (!_isInitialized)
            throw new InvalidOperationException("VectorGraphicsEngine is not initialized. Call InitializeAsync first.");
    }
}
