﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Silk.NET.Core" Version="2.22.0" />
    <PackageReference Include="Silk.NET.Direct3D11" Version="2.22.0" />
    <PackageReference Include="Silk.NET.Vulkan" Version="2.22.0" />
    <PackageReference Include="SkiaSharp" Version="3.119.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.119.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.macOS" Version="3.119.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\modules\src\Abstractions\ArtDesignFramework.Abstractions.csproj" />
  </ItemGroup>


</Project>
