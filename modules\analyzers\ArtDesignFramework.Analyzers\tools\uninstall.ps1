# ArtDesignFramework Analyzers Uninstallation Script
# Last Updated: 2025-06-12 09:05:00 UTC
# Removes architectural compliance analyzers from the framework

param($installPath, $toolsPath, $package, $project)

Write-Host "Uninstalling ArtDesignFramework.Analyzers..." -ForegroundColor Yellow

try {
    # Remove analyzer reference from the project
    $analyzerPath = Join-Path $toolsPath "..\analyzers\dotnet\cs\ArtDesignFramework.Analyzers.dll"
    
    # Remove the analyzer from the project
    $project.Object.References | Where-Object { $_.Path -eq $analyzerPath } | ForEach-Object { $_.Remove() }
    
    Write-Host "ArtDesignFramework.Analyzers uninstalled successfully!" -ForegroundColor Green
}
catch {
    Write-Error "Failed to uninstall ArtDesignFramework.Analyzers: $_"
}
