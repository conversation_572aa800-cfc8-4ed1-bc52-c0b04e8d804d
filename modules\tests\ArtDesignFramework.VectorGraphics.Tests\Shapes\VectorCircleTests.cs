using System.Numerics;
using ArtDesignFramework.TestFramework.Core;
using ArtDesignFramework.VectorGraphics.Core.Models;
using ArtDesignFramework.VectorGraphics.Shapes;
using SkiaSharp;

namespace ArtDesignFramework.VectorGraphics.Tests.Shapes;

/// <summary>
/// Comprehensive unit tests for the VectorCircle class.
/// Tests circle creation, properties, path generation, and rendering.
/// Integrates with the TestFramework for enterprise-grade validation.
/// </summary>
[TestFixture]
[Category("VectorGraphics")]
[Category("Shapes")]
[Category("Circle")]
public class VectorCircleTests
{
    private VectorCircle _circle;

    [SetUp]
    public void SetUp()
    {
        _circle = new VectorCircle(0, 0, 0);
    }

    [TearDown]
    public void TearDown()
    {
        _circle?.Dispose();
    }

    [Test]
    [Category("Construction")]
    public void Constructor_ShouldCreateValidCircle()
    {
        // Assert
        _circle.Should().NotBeNull();
        _circle.Id.Should().NotBeNullOrEmpty();
        _circle.Center.Should().Be(new Vector2(0, 0));
        _circle.Radius.Should().Be(0);
        _circle.IsVisible.Should().BeTrue();
    }

    [Test]
    [Category("Properties")]
    public void Center_WhenSet_ShouldUpdateCorrectly()
    {
        // Arrange
        var newCenter = new Vector2(100, 150);

        // Act
        _circle.SetCenter(newCenter);

        // Assert
        _circle.Center.Should().Be(newCenter);
    }

    [Test]
    [Category("Properties")]
    public void Radius_WhenSetToPositiveValue_ShouldUpdateCorrectly()
    {
        // Arrange
        const float newRadius = 50.5f;

        // Act
        _circle.SetRadius(newRadius);

        // Assert
        _circle.Radius.Should().Be(newRadius);
    }

    [Test]
    [Category("Properties")]
    public void Radius_WhenSetToNegativeValue_ShouldClampToZero()
    {
        // Act
        _circle.SetRadius(-10);

        // Assert - SetRadius clamps negative values to 0
        _circle.Radius.Should().Be(0);
    }

    [Test]
    [Category("Properties")]
    public void Radius_WhenSetToZero_ShouldBeAllowed()
    {
        // Act
        _circle.SetRadius(0);

        // Assert
        _circle.Radius.Should().Be(0);
    }

    [Test]
    [Category("Bounds")]
    public void Bounds_WithCenterAndRadius_ShouldCalculateCorrectly()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);

        // Act
        var bounds = _circle.Bounds;

        // Assert
        bounds.X.Should().Be(50);
        bounds.Y.Should().Be(50);
        bounds.Width.Should().Be(100);
        bounds.Height.Should().Be(100);
    }

    [Test]
    [Category("Bounds")]
    public void Bounds_WithZeroRadius_ShouldReturnPointBounds()
    {
        // Arrange
        _circle.SetCenter(75, 25);
        _circle.SetRadius(0);

        // Act
        var bounds = _circle.Bounds;

        // Assert
        bounds.X.Should().Be(75);
        bounds.Y.Should().Be(25);
        bounds.Width.Should().Be(0);
        bounds.Height.Should().Be(0);
    }

    [Test]
    [Category("PathGeneration")]
    public void CreatePath_WithValidCircle_ShouldReturnValidPath()
    {
        // Arrange
        _circle.SetCenter(50, 50);
        _circle.SetRadius(25);

        // Act
        using var path = _circle.CreatePath();

        // Assert
        path.Should().NotBeNull();
        path.IsEmpty.Should().BeFalse();

        var bounds = path.Bounds;
        bounds.Left.Should().BeApproximately(25, 1);
        bounds.Top.Should().BeApproximately(25, 1);
        bounds.Right.Should().BeApproximately(75, 1);
        bounds.Bottom.Should().BeApproximately(75, 1);
    }

    [Test]
    [Category("PathGeneration")]
    public void CreatePath_WithZeroRadius_ShouldReturnValidPath()
    {
        // Arrange
        _circle.SetCenter(50, 50);
        _circle.SetRadius(0);

        // Act
        using var path = _circle.CreatePath();

        // Assert
        path.Should().NotBeNull();
        // Note: Zero radius circle may still create a valid path, just very small
    }

    [Test]
    [Category("HitTesting")]
    public void ContainsPoint_WithPointInsideCircle_ShouldReturnTrue()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);
        var testPoint = new Vector2(110, 110); // Inside circle

        // Act
        var result = _circle.ContainsPoint(testPoint);

        // Assert
        result.Should().BeTrue();
    }

    [Test]
    [Category("HitTesting")]
    public void ContainsPoint_WithPointOutsideCircle_ShouldReturnFalse()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);
        var testPoint = new Vector2(200, 200); // Outside circle

        // Act
        var result = _circle.ContainsPoint(testPoint);

        // Assert
        result.Should().BeFalse();
    }

    [Test]
    [Category("HitTesting")]
    public void ContainsPoint_WithPointOnCircleEdge_ShouldReturnTrue()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);
        var testPoint = new Vector2(150, 100); // On circle edge

        // Act
        var result = _circle.ContainsPoint(testPoint);

        // Assert
        result.Should().BeTrue();
    }

    [Test]
    [Category("HitTesting")]
    public void ContainsPoint_WithCenterPoint_ShouldReturnTrue()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);
        var testPoint = new Vector2(100, 100); // Center point

        // Act
        var result = _circle.ContainsPoint(testPoint);

        // Assert
        result.Should().BeTrue();
    }

    [Test]
    [Category("Cloning")]
    public void Clone_ShouldCreateIdenticalCopy()
    {
        // Arrange
        _circle.SetCenter(75, 125);
        _circle.SetRadius(30);
        _circle.IsVisible = false;

        // Act
        var cloned = _circle.Clone() as VectorCircle;

        // Assert
        cloned.Should().NotBeNull();
        cloned.Should().NotBeSameAs(_circle);
        cloned!.Center.Should().Be(_circle.Center);
        cloned.Radius.Should().Be(_circle.Radius);
        cloned.IsVisible.Should().Be(_circle.IsVisible);
        cloned.Id.Should().NotBe(_circle.Id); // Should have different ID
    }

    [Test]
    [Category("Styling")]
    public void Fill_WhenSet_ShouldUpdateCorrectly()
    {
        // Arrange
        var fill = VectorFill.Solid(SKColors.Red);

        // Act
        _circle.Fill = fill;

        // Assert
        _circle.Fill.Should().Be(fill);
        _circle.Fill.Color.Should().Be(SKColors.Red);
    }

    [Test]
    [Category("Styling")]
    public void Stroke_WhenSet_ShouldUpdateCorrectly()
    {
        // Arrange
        var stroke = VectorStroke.Solid(SKColors.Blue, 2);

        // Act
        _circle.Stroke = stroke;

        // Assert
        _circle.Stroke.Should().Be(stroke);
        _circle.Stroke.Color.Should().Be(SKColors.Blue);
        _circle.Stroke.Width.Should().Be(2);
    }

    [Test]
    [Category("Transform")]
    public void Transform_WhenSet_ShouldUpdateCorrectly()
    {
        // Arrange
        var transform = Matrix3x2.CreateTranslation(10, 20) * Matrix3x2.CreateScale(2, 2);

        // Act
        _circle.Transform = transform;

        // Assert
        _circle.Transform.Should().Be(transform);
    }

    [Test]
    [Category("Rendering")]
    public void Render_WithValidContext_ShouldNotThrow()
    {
        // Arrange
        _circle.SetCenter(50, 50);
        _circle.SetRadius(25);

        using var surface = SKSurface.Create(new SKImageInfo(100, 100));
        using var canvas = surface.Canvas;
        var context = VectorRenderContext.Default(100, 100);

        // Act & Assert
        var act = () => _circle.Render(canvas, context);
        act.Should().NotThrow();
    }

    [Test]
    [Category("Performance")]
    [CancelAfter(1000)]
    public void CreatePath_PerformanceTest_ShouldCompleteQuickly()
    {
        // Arrange
        _circle.SetCenter(100, 100);
        _circle.SetRadius(50);

        // Act & Assert
        for (int i = 0; i < 1000; i++)
        {
            using var path = _circle.CreatePath();
            path.Should().NotBeNull();
        }
    }

    [Test]
    [Category("EdgeCases")]
    public void Circle_WithVeryLargeRadius_ShouldHandleCorrectly()
    {
        // Arrange
        const float largeRadius = 1000000f;

        // Act
        _circle.SetRadius(largeRadius);

        // Assert
        _circle.Radius.Should().Be(largeRadius);
        var bounds = _circle.Bounds;
        bounds.Width.Should().Be(largeRadius * 2);
        bounds.Height.Should().Be(largeRadius * 2);
    }

    [Test]
    [Category("EdgeCases")]
    public void Circle_WithVerySmallRadius_ShouldHandleCorrectly()
    {
        // Arrange
        const float smallRadius = 0.001f;

        // Act
        _circle.SetRadius(smallRadius);

        // Assert
        _circle.Radius.Should().Be(smallRadius);
        using var path = _circle.CreatePath();
        path.Should().NotBeNull();
    }
}
