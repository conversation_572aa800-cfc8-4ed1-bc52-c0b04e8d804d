{"Logging": {"LogLevel": {"Default": "Information", "ArtDesignFramework": "Debug", "Microsoft": "Warning", "System": "Warning"}}, "Database": {"ConnectionString": "Data Source=:memory:", "EnableSensitiveDataLogging": false, "CommandTimeout": 30}, "Performance": {"EnableProfiling": true, "EnableMemoryTracking": true, "ProfilerSampleRate": 1000, "MemoryTrackingInterval": 5000}, "PluginSystem": {"PluginsDirectory": "TestPlugins", "InitializeOnStartup": false, "AutoStartPlugins": false, "EnableSandboxing": false, "MaxPluginLoadTime": 10000}, "EffectsEngine": {"EnableGpuAcceleration": false, "MaxConcurrentEffects": 10, "EffectTimeout": 30000}, "VectorGraphics": {"EnableHardwareAcceleration": false, "MaxCanvasSize": 4096, "DefaultDpi": 96}, "AILighting": {"EnableGpuCompute": false, "MaxLightSources": 8, "ComputeTimeout": 15000}, "AIModelManager": {"EnableModelCaching": true, "ModelCacheDirectory": "TestModelCache", "MaxCacheSize": 1073741824, "ModelTimeout": 30000}, "FreeFonts": {"CacheDirectory": "TestFontCache", "EnableFontCaching": true, "MaxCacheSize": 536870912, "FontLoadTimeout": 10000}, "ClockDesktopApp": {"EnableTransparency": true, "DefaultUpdateInterval": 1000, "MaxClockInstances": 10, "EnableServerStorage": false}, "TestFramework": {"EnablePerformanceTests": true, "EnableIntegrationTests": true, "TestTimeout": 300000, "MaxConcurrentTests": 4, "EnableDetailedLogging": true, "PerformanceThresholds": {"FrameworkInitialization": 3000, "ServiceResolution": 1, "AbilityRegistration": 50, "AbilityRetrieval": 10, "AbilityUnregistration": 50, "ConcurrentOperations": 100, "MemoryUsageIncrease": 52428800}}, "Integration": {"EnableStressTests": false, "StressTestDuration": 60000, "StressTestConcurrency": 20, "EnableMemoryProfiling": true, "EnablePerformanceProfiling": true, "ValidationTimeout": 30000}}