<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <PropertyGroup>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core test packages -->
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NUnit" Version="4.0.1" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />

    <!-- Test-specific packages not provided by Directory.Build.props -->
    <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="BenchmarkDotNet" Version="0.13.12" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\VectorGraphics\ArtDesignFramework.VectorGraphics.csproj" />
    <ProjectReference Include="..\..\src\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\..\src\TestFramework\ArtDesignFramework.TestFramework.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
    <Using Include="FluentAssertions" />
    <Using Include="Moq" />
    <Using Include="SkiaSharp" />
  </ItemGroup>

</Project>
