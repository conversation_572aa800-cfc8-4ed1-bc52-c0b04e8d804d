# ArtDesignFramework.HealthMonitoring

Automated integrity monitoring system that continuously validates framework architectural integrity, detects violations early, and provides real-time feedback on architectural health metrics.

**Last Updated: 2025-01-27 22:55:00 UTC**

## Overview

The HealthMonitoring module provides comprehensive automated monitoring capabilities for the ArtDesignFramework, ensuring that the framework maintains its 90%+ architectural integrity target through continuous validation, early detection of issues, and automated remediation capabilities.

## Features

### 🔍 **Comprehensive Health Monitoring**
- **Module Compilation Validation**: Continuous compilation status monitoring
- **Dependency Health Checks**: Automated dependency validation and circular dependency detection
- **Architectural Compliance**: Integration with ADF analyzers for compliance monitoring
- **Performance Metrics**: Real-time performance monitoring and trend analysis
- **Service Registration Validation**: Ensures proper service registration patterns

### 🚨 **Intelligent Alerting System**
- **Threshold-Based Alerts**: Configurable thresholds for health scores, response times, and error rates
- **Severity Classification**: Critical, Error, Warning, and Info level alerts
- **Alert Acknowledgment**: Track alert resolution and acknowledgment
- **Predictive Alerting**: Early warning system based on trend analysis

### 📊 **Advanced Analytics**
- **Health Trend Analysis**: Historical trend analysis with predictive insights
- **System Health Dashboard**: Real-time overview of framework health
- **Performance Benchmarking**: Continuous performance validation
- **Health Score Calculation**: Comprehensive scoring based on multiple metrics

### 🔧 **Automated Remediation**
- **Self-Healing Capabilities**: Automatic remediation for common issues
- **Compilation Fixes**: Automated restore and rebuild attempts
- **Dependency Resolution**: Automatic dependency issue resolution
- **Performance Optimization**: Memory cleanup and optimization

### 📈 **Enterprise Features**
- **Historical Data Retention**: Configurable data retention policies
- **Export Capabilities**: JSON, CSV, and XML export formats
- **Integration APIs**: RESTful APIs for external monitoring systems
- **Real-Time Updates**: SignalR integration for live dashboard updates

## Quick Start

### Basic Setup

```csharp
// Program.cs or Startup.cs
services.AddAutomatedIntegrityMonitoring(configuration);
```

### Enterprise Setup

```csharp
// For enterprise-grade monitoring with enhanced features
services.AddEnterpriseHealthMonitoring(configuration);
```

### Custom Configuration

```csharp
services.AddAutomatedIntegrityMonitoring(options =>
{
    options.CheckInterval = TimeSpan.FromMinutes(1);
    options.EnablePredictiveMonitoring = true;
    options.EnableAutoRemediation = true;
    options.AlertThresholds["HealthScore"] = 0.9; // 90%
    options.ModulesToMonitor.AddRange(new[] { "Core", "UserInterface", "Performance" });
});
```

### Specific Module Monitoring

```csharp
services.AddAutomatedIntegrityMonitoringForModules("Core", "UserInterface", "Performance");
```

## Configuration

### appsettings.json

```json
{
  "HealthMonitoring": {
    "CheckInterval": "00:05:00",
    "CheckTimeout": "00:00:30",
    "EnablePredictiveMonitoring": true,
    "EnableAutoRemediation": false,
    "MaxHealthRecords": 1000,
    "HealthRecordRetention": "7.00:00:00",
    "EnableDetailedLogging": false,
    "ModulesToMonitor": [
      "Core",
      "Abstractions",
      "TestFramework",
      "UserInterface",
      "Performance",
      "DataAccess"
    ],
    "AlertThresholds": {
      "HealthScore": 0.8,
      "ResponseTime": 5000,
      "ErrorRate": 0.05,
      "MemoryUsage": 0.8
    }
  }
}
```

## Usage Examples

### Basic Health Monitoring

```csharp
public class HealthController : ControllerBase
{
    private readonly IModuleHealthMonitor _healthMonitor;

    public HealthController(IModuleHealthMonitor healthMonitor)
    {
        _healthMonitor = healthMonitor;
    }

    [HttpGet("health/{moduleName}")]
    public async Task<IActionResult> GetModuleHealth(string moduleName)
    {
        var health = await _healthMonitor.CheckModuleHealthAsync(moduleName);
        return Ok(health);
    }

    [HttpGet("health/system")]
    public async Task<IActionResult> GetSystemHealth()
    {
        var summary = await _healthMonitor.GetSystemHealthSummaryAsync();
        return Ok(summary);
    }

    [HttpGet("health/alerts")]
    public async Task<IActionResult> GetActiveAlerts()
    {
        var alerts = await _healthMonitor.GetActiveAlertsAsync();
        return Ok(alerts);
    }
}
```

### Custom Health Checks

```csharp
// Register custom health check
await healthMonitor.RegisterHealthCheckAsync("MyModule", "CustomCheck", async (cancellationToken) =>
{
    // Perform custom validation
    var isHealthy = await ValidateCustomLogicAsync();
    
    return new HealthCheckResult
    {
        CheckName = "CustomCheck",
        Description = "Custom validation logic",
        IsHealthy = isHealthy,
        Severity = isHealthy ? HealthCheckSeverity.Info : HealthCheckSeverity.Warning,
        Message = isHealthy ? "Custom check passed" : "Custom check failed"
    };
});
```

### Health Trend Analysis

```csharp
// Analyze health trends
var trend = await healthMonitor.AnalyzeHealthTrendAsync("Core", "HealthScore", TimeSpan.FromDays(7));

if (trend.Direction == TrendDirection.Degrading && trend.Strength > 0.2)
{
    // Take action based on degrading trend
    logger.LogWarning("Core module health is degrading: {Recommendations}", 
        string.Join(", ", trend.Recommendations));
}
```

### Predictive Insights

```csharp
// Get predictive insights
var insights = await healthMonitor.GetPredictiveInsightsAsync("UserInterface", TimeSpan.FromHours(24));

foreach (var insight in insights.Where(i => i.Direction == TrendDirection.Degrading))
{
    logger.LogWarning("Predicted degradation in {Module} {Metric}: {PredictedValue}", 
        insight.ModuleName, insight.MetricName, insight.PredictedValue);
}
```

## Health Check Types

### 1. Compilation Health Check
- Validates that modules compile successfully
- Detects compilation errors and warnings
- Tracks build performance metrics

### 2. Dependency Health Check
- Validates project references exist and are valid
- Detects circular dependencies
- Monitors dependency resolution issues

### 3. Architectural Compliance Check
- Integrates with ADF analyzers (ADF0001-ADF0004)
- Validates MVVM patterns and [TestableMethod] attributes
- Monitors service registration compliance

### 4. Performance Health Check
- Monitors memory usage and CPU performance
- Tracks response times and throughput
- Validates performance test coverage

### 5. Service Registration Check
- Validates ServiceCollectionExtensions exist
- Monitors service registration patterns
- Detects missing or incorrect registrations

## Health Scoring

The health scoring system uses a comprehensive algorithm that considers:

- **Base Score**: Percentage of passing health checks
- **Severity Penalties**: Critical failures (-30%), Error failures (-20%)
- **Trend Analysis**: Historical performance trends
- **Compliance Metrics**: Architectural compliance scores

### Health Status Levels

- **Healthy** (80-100%): All systems functioning optimally
- **Degraded** (60-79%): Some issues detected, monitoring required
- **Unhealthy** (0-59%): Significant issues requiring attention
- **Critical** (Any critical failures): Immediate action required

## Integration with Framework

### Build Integration

The health monitoring system integrates with the build process through:

- **MSBuild Targets**: Automated validation during build
- **Roslyn Analyzers**: Real-time compliance checking
- **CI/CD Integration**: Continuous monitoring in pipelines

### Testing Integration

- **Integration Tests**: Validates module interactions
- **Performance Tests**: Monitors performance regressions
- **Health Check Tests**: Validates monitoring system itself

## Monitoring Dashboard

The health monitoring system provides a real-time dashboard showing:

- **System Overview**: Overall health score and status
- **Module Status**: Individual module health and trends
- **Active Alerts**: Current alerts and their severity
- **Performance Metrics**: Real-time performance data
- **Historical Trends**: Long-term health trends and predictions

## Best Practices

### 1. Configuration
- Set appropriate check intervals based on system load
- Configure alert thresholds based on system requirements
- Enable auto-remediation only in controlled environments

### 2. Custom Health Checks
- Keep health checks lightweight and fast
- Use appropriate severity levels for different types of issues
- Provide meaningful error messages and recommendations

### 3. Alert Management
- Acknowledge alerts promptly to maintain system hygiene
- Review alert thresholds regularly and adjust as needed
- Use predictive insights to prevent issues before they occur

### 4. Performance
- Monitor the monitoring system's own performance impact
- Use appropriate data retention policies to manage storage
- Consider using sampling for high-frequency metrics

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Adjust MaxHealthRecords and HealthRecordRetention
2. **Slow Health Checks**: Increase CheckTimeout or optimize custom checks
3. **False Alerts**: Review and adjust alert thresholds
4. **Missing Modules**: Verify module paths and project file existence

### Debugging

Enable detailed logging for troubleshooting:

```json
{
  "HealthMonitoring": {
    "EnableDetailedLogging": true
  },
  "Logging": {
    "LogLevel": {
      "ArtDesignFramework.HealthMonitoring": "Debug"
    }
  }
}
```

## Architecture

The health monitoring system follows a modular architecture:

- **AutomatedIntegrityMonitor**: Core monitoring service
- **Health Check Providers**: Specific health check implementations
- **Alert System**: Alert generation and management
- **Data Storage**: In-memory caching with configurable retention
- **Export System**: Multiple format support for data export

## Performance Considerations

- **Memory Usage**: Configurable data retention limits
- **CPU Impact**: Optimized health check scheduling
- **I/O Operations**: Efficient file system and process monitoring
- **Network Impact**: Minimal network usage for local monitoring

## Security

- **Access Control**: Integration with framework security model
- **Data Protection**: Secure handling of health data
- **Audit Trail**: Complete audit trail of all health events
- **Privacy**: No sensitive data stored in health records

---

**Note**: This module is essential for maintaining the framework's 90%+ architectural integrity target and should be included in all production deployments.
