// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Diagnostics;
using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
using ArtDesignFramework.TestFramework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Xunit.Abstractions;

namespace ArtDesignFramework.Integration.Tests.Infrastructure;

/// <summary>
/// Base class for integration tests providing comprehensive framework setup and validation
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
public abstract class IntegrationTestBase : IDisposable
{
    protected readonly ITestOutputHelper Output;
    protected readonly ServiceProvider ServiceProvider;
    protected readonly ILogger Logger;
    protected readonly IFrameworkService FrameworkService;
    protected readonly IConfiguration Configuration;
    protected readonly Stopwatch TestStopwatch;

    /// <summary>
    /// Initializes integration test base with comprehensive framework setup
    /// </summary>
    /// <param name="output">Test output helper for logging</param>
    protected IntegrationTestBase(ITestOutputHelper output)
    {
        Output = output ?? throw new ArgumentNullException(nameof(output));
        TestStopwatch = Stopwatch.StartNew();

        var services = new ServiceCollection();
        Configuration = BuildConfiguration();

        ConfigureLogging(services);
        ConfigureFrameworkServices(services);
        ConfigureModuleServices(services);
        ConfigureTestServices(services);

        ServiceProvider = services.BuildServiceProvider();
        Logger = ServiceProvider.GetRequiredService<ILogger<IntegrationTestBase>>();
        FrameworkService = ServiceProvider.GetRequiredService<IFrameworkService>();

        Logger.LogInformation("Integration test base initialized for {TestType}", GetType().Name);
    }

    /// <summary>
    /// Builds configuration for integration tests
    /// </summary>
    /// <returns>Configuration instance</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Logging:LogLevel:Default"] = "Information",
                ["Logging:LogLevel:ArtDesignFramework"] = "Debug",
                ["Database:ConnectionString"] = "Data Source=:memory:",
                ["Performance:EnableProfiling"] = "true",
                ["Performance:EnableMemoryTracking"] = "true",
                ["PluginSystem:PluginsDirectory"] = "TestPlugins",
                ["PluginSystem:InitializeOnStartup"] = "false",
                ["EffectsEngine:EnableGpuAcceleration"] = "false",
                ["VectorGraphics:EnableHardwareAcceleration"] = "false",
                ["AILighting:EnableGpuCompute"] = "false",
                ["FreeFonts:CacheDirectory"] = "TestFontCache"
            })
            .Build();
    }

    /// <summary>
    /// Configures logging services for integration tests
    /// </summary>
    /// <param name="services">Service collection</param>
    private void ConfigureLogging(IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddXUnit(Output)
                   .SetMinimumLevel(LogLevel.Debug)
                   .AddFilter("Microsoft", LogLevel.Warning)
                   .AddFilter("System", LogLevel.Warning);
        });
    }

    /// <summary>
    /// Configures core framework services
    /// </summary>
    /// <param name="services">Service collection</param>
    private void ConfigureFrameworkServices(IServiceCollection services)
    {
        services.AddSingleton(Configuration);
        services.AddArtDesignFrameworkCore();
        services.AddArtDesignFrameworkTestFramework();
    }

    /// <summary>
    /// Configures core module services for integration testing
    /// </summary>
    /// <param name="services">Service collection</param>
    private void ConfigureModuleServices(IServiceCollection services)
    {
        // For now, only configure core framework services
        // Additional modules can be added as they become available
        Logger?.LogInformation("Core module services configured for integration testing");
    }

    /// <summary>
    /// Configures test-specific services and mocks
    /// </summary>
    /// <param name="services">Service collection</param>
    private void ConfigureTestServices(IServiceCollection services)
    {
        // Add test-specific configurations
        services.Configure<TestFrameworkOptions>(options =>
        {
            options.EnablePerformanceTests = true;
            options.EnableIntegrationTests = true;
            options.TestTimeout = TimeSpan.FromMinutes(5);
            options.MaxConcurrentTests = Environment.ProcessorCount;
        });
    }

    /// <summary>
    /// Validates that all framework services are properly registered and initialized
    /// </summary>
    /// <returns>Task representing the validation operation</returns>
    [TestableMethod("ValidateFrameworkInitialization", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    protected async Task ValidateFrameworkInitializationAsync()
    {
        Logger.LogInformation("Validating framework initialization");

        // Validate core services
        var coreServices = new[]
        {
            typeof(IFrameworkService),
            typeof(IAbilityRegistry),
            typeof(ICommandManager),
            typeof(ILayerManager)
        };

        foreach (var serviceType in coreServices)
        {
            var service = ServiceProvider.GetService(serviceType);
            if (service == null)
            {
                throw new InvalidOperationException($"Core service {serviceType.Name} is not registered");
            }
            Logger.LogDebug("Core service {ServiceType} validated", serviceType.Name);
        }

        // Initialize framework
        await FrameworkService.InitializeAsync();

        // Validate framework state
        if (!FrameworkService.IsInitialized)
        {
            throw new InvalidOperationException("Framework failed to initialize");
        }

        Logger.LogInformation("Framework initialization validation completed successfully");
    }

    /// <summary>
    /// Validates service registration and dependency injection configuration
    /// </summary>
    /// <returns>Task representing the validation operation</returns>
    [TestableMethod("ValidateServiceRegistration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    protected async Task ValidateServiceRegistrationAsync()
    {
        Logger.LogInformation("Validating service registration");

        var requiredServices = GetRequiredServiceTypes();
        var missingServices = new List<Type>();

        foreach (var serviceType in requiredServices)
        {
            try
            {
                var service = ServiceProvider.GetRequiredService(serviceType);
                if (service == null)
                {
                    missingServices.Add(serviceType);
                }
                else
                {
                    Logger.LogDebug("Service {ServiceType} registered successfully", serviceType.Name);
                }
            }
            catch (InvalidOperationException)
            {
                missingServices.Add(serviceType);
            }
        }

        if (missingServices.Any())
        {
            var missingServiceNames = string.Join(", ", missingServices.Select(t => t.Name));
            throw new InvalidOperationException($"Missing required services: {missingServiceNames}");
        }

        Logger.LogInformation("Service registration validation completed successfully");
        await Task.CompletedTask;
    }

    /// <summary>
    /// Gets the list of required service types for validation
    /// </summary>
    /// <returns>Array of required service types</returns>
    protected virtual Type[] GetRequiredServiceTypes()
    {
        return new[]
        {
            // Core services
            typeof(IFrameworkService),
            typeof(IAbilityRegistry),
            typeof(ICommandManager),
            typeof(ILayerManager),

            // Module services
            typeof(ILogger<>),
            typeof(IConfiguration)
        };
    }

    /// <summary>
    /// Measures and validates performance metrics for the test
    /// </summary>
    /// <param name="operationName">Name of the operation being measured</param>
    /// <param name="maxExpectedDuration">Maximum expected duration</param>
    /// <returns>Task representing the performance validation</returns>
    [TestableMethod("ValidatePerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    protected async Task ValidatePerformanceAsync(string operationName, TimeSpan maxExpectedDuration)
    {
        if (string.IsNullOrWhiteSpace(operationName))
            throw new ArgumentException("Operation name cannot be null or empty", nameof(operationName));

        var elapsed = TestStopwatch.Elapsed;

        Logger.LogInformation("Performance validation for {OperationName}: {ElapsedMs}ms (max: {MaxMs}ms)",
            operationName, elapsed.TotalMilliseconds, maxExpectedDuration.TotalMilliseconds);

        if (elapsed > maxExpectedDuration)
        {
            Logger.LogWarning("Performance threshold exceeded for {OperationName}: {ElapsedMs}ms > {MaxMs}ms",
                operationName, elapsed.TotalMilliseconds, maxExpectedDuration.TotalMilliseconds);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// Disposes resources used by the integration test base
    /// </summary>
    public virtual void Dispose()
    {
        TestStopwatch?.Stop();
        Logger?.LogInformation("Integration test completed in {ElapsedMs}ms", TestStopwatch?.ElapsedMilliseconds ?? 0);
        ServiceProvider?.Dispose();
        GC.SuppressFinalize(this);
    }
}
