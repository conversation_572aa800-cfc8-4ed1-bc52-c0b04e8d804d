// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
using ArtDesignFramework.Integration.Tests.Infrastructure;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace ArtDesignFramework.Integration.Tests;

/// <summary>
/// Integration tests for dependency injection container configuration and service resolution
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
public class DependencyInjectionIntegrationTests : IntegrationTestBase
{
    public DependencyInjectionIntegrationTests(ITestOutputHelper output) : base(output)
    {
    }

    /// <summary>
    /// Tests that all required services are properly registered
    /// </summary>
    [Fact]
    [TestableMethod("RequiredServicesRegistration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task RequiredServices_ShouldBeRegistered()
    {
        // Arrange
        Logger.LogInformation("Testing required services registration");

        // Act & Assert - Validate service registration
        await ValidateServiceRegistrationAsync();

        // Act - Test service resolution directly (we can't get IServiceCollection from provider)
        var registeredServiceTypes = new HashSet<Type>();

        // Test core services by attempting to resolve them
        var coreServiceTypes = new[]
        {
            typeof(IFrameworkService),
            typeof(IAbilityRegistry),
            typeof(ICommandManager),
            typeof(ILayerManager),
            typeof(ILogger<DependencyInjectionIntegrationTests>),
            typeof(IConfiguration)
        };

        foreach (var serviceType in coreServiceTypes)
        {
            try
            {
                var service = ServiceProvider.GetService(serviceType);
                if (service != null)
                {
                    registeredServiceTypes.Add(serviceType);
                }
            }
            catch
            {
                // Service not registered or resolution failed
            }
        }

        // Assert - Core framework services should be registered
        foreach (var serviceType in coreServiceTypes)
        {
            if (serviceType.IsGenericTypeDefinition)
            {
                // For generic types like ILogger<>, we tested a concrete version
                var hasGenericService = registeredServiceTypes.Any(t =>
                    t.IsGenericType && t.GetGenericTypeDefinition() == serviceType);
                hasGenericService.Should().BeTrue($"Generic service {serviceType.Name} should be registered");
            }
            else
            {
                registeredServiceTypes.Should().Contain(serviceType, $"Service {serviceType.Name} should be registered");
            }
        }

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("RequiredServicesRegistration", TimeSpan.FromSeconds(2));

        Logger.LogInformation("Required services registration test completed successfully");
    }

    /// <summary>
    /// Tests service lifetime configurations
    /// </summary>
    [Fact]
    [TestableMethod("ServiceLifetimeConfiguration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1500)]
    public async Task ServiceLifetimes_ShouldBeConfiguredCorrectly()
    {
        // Arrange
        Logger.LogInformation("Testing service lifetime configurations");

        // Act & Assert - Test singleton services
        var frameworkService1 = ServiceProvider.GetRequiredService<IFrameworkService>();
        var frameworkService2 = ServiceProvider.GetRequiredService<IFrameworkService>();
        frameworkService1.Should().BeSameAs(frameworkService2, "IFrameworkService should be singleton");

        var abilityRegistry1 = ServiceProvider.GetRequiredService<IAbilityRegistry>();
        var abilityRegistry2 = ServiceProvider.GetRequiredService<IAbilityRegistry>();
        abilityRegistry1.Should().BeSameAs(abilityRegistry2, "IAbilityRegistry should be singleton");

        var commandManager1 = ServiceProvider.GetRequiredService<ICommandManager>();
        var commandManager2 = ServiceProvider.GetRequiredService<ICommandManager>();
        commandManager1.Should().BeSameAs(commandManager2, "ICommandManager should be singleton");

        // Act & Assert - Test scoped services with different scopes
        using var scope1 = ServiceProvider.CreateScope();
        using var scope2 = ServiceProvider.CreateScope();

        var scopedLogger1 = scope1.ServiceProvider.GetRequiredService<ILogger<DependencyInjectionIntegrationTests>>();
        var scopedLogger2 = scope2.ServiceProvider.GetRequiredService<ILogger<DependencyInjectionIntegrationTests>>();

        // Loggers can be the same instance or different depending on implementation
        // The important thing is they both resolve successfully
        scopedLogger1.Should().NotBeNull("Scoped logger should resolve in scope 1");
        scopedLogger2.Should().NotBeNull("Scoped logger should resolve in scope 2");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ServiceLifetimeConfiguration", TimeSpan.FromMilliseconds(1500));

        Logger.LogInformation("Service lifetime configuration test completed successfully");
    }

    /// <summary>
    /// Tests circular dependency detection and prevention
    /// </summary>
    [Fact]
    [TestableMethod("CircularDependencyPrevention", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task CircularDependencies_ShouldBePreventedOrHandled()
    {
        // Arrange
        Logger.LogInformation("Testing circular dependency prevention");

        // Act & Assert - Test that core services can be resolved without circular dependency issues
        var coreServiceTypes = new[]
        {
            typeof(IFrameworkService),
            typeof(IAbilityRegistry),
            typeof(ICommandManager),
            typeof(ILayerManager)
        };

        foreach (var serviceType in coreServiceTypes)
        {
            try
            {
                var service = ServiceProvider.GetRequiredService(serviceType);
                service.Should().NotBeNull($"Service {serviceType.Name} should resolve without circular dependency issues");
                Logger.LogDebug("Service {ServiceType} resolved successfully", serviceType.Name);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("circular"))
            {
                Assert.True(false, $"Circular dependency detected for {serviceType.Name}: {ex.Message}");
            }
        }

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("CircularDependencyPrevention", TimeSpan.FromSeconds(1));

        Logger.LogInformation("Circular dependency prevention test completed successfully");
    }

    /// <summary>
    /// Tests service resolution performance under load
    /// </summary>
    [Fact]
    [TestableMethod("ServiceResolutionPerformance", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 3000)]
    public async Task ServiceResolution_ShouldPerformWellUnderLoad()
    {
        // Arrange
        Logger.LogInformation("Testing service resolution performance under load");
        const int iterations = 1000;
        const int concurrentTasks = 10;

        // Act - Perform concurrent service resolutions
        var tasks = Enumerable.Range(0, concurrentTasks)
            .Select(async taskId =>
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var resolutionTimes = new List<long>();

                for (int i = 0; i < iterations / concurrentTasks; i++)
                {
                    var iterationStopwatch = System.Diagnostics.Stopwatch.StartNew();

                    // Resolve various services
                    var frameworkService = ServiceProvider.GetRequiredService<IFrameworkService>();
                    var abilityRegistry = ServiceProvider.GetRequiredService<IAbilityRegistry>();
                    var commandManager = ServiceProvider.GetRequiredService<ICommandManager>();
                    var logger = ServiceProvider.GetRequiredService<ILogger<DependencyInjectionIntegrationTests>>();

                    iterationStopwatch.Stop();
                    resolutionTimes.Add(iterationStopwatch.ElapsedTicks);

                    // Verify services are not null
                    frameworkService.Should().NotBeNull();
                    abilityRegistry.Should().NotBeNull();
                    commandManager.Should().NotBeNull();
                    logger.Should().NotBeNull();
                }

                stopwatch.Stop();
                return new
                {
                    TaskId = taskId,
                    TotalTime = stopwatch.ElapsedMilliseconds,
                    AverageResolutionTime = resolutionTimes.Average(),
                    MaxResolutionTime = resolutionTimes.Max(),
                    MinResolutionTime = resolutionTimes.Min()
                };
            });

        var results = await Task.WhenAll(tasks);

        // Assert - Performance metrics
        var totalTime = results.Sum(r => r.TotalTime);
        var averageTaskTime = totalTime / (double)concurrentTasks;
        var overallAverageResolutionTime = results.Average(r => r.AverageResolutionTime);

        Logger.LogInformation("Performance metrics - Total time: {TotalTime}ms, Average task time: {AverageTaskTime}ms, " +
                             "Average resolution time: {AverageResolutionTime} ticks",
            totalTime, averageTaskTime, overallAverageResolutionTime);

        // Performance assertions
        averageTaskTime.Should().BeLessThan(1000, "Average task time should be under 1 second");
        overallAverageResolutionTime.Should().BeLessThan(10000, "Average resolution time should be reasonable");

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ServiceResolutionPerformance", TimeSpan.FromSeconds(3));

        Logger.LogInformation("Service resolution performance test completed successfully");
    }

    /// <summary>
    /// Tests service disposal and cleanup
    /// </summary>
    [Fact]
    [TestableMethod("ServiceDisposalAndCleanup", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task ServiceDisposal_ShouldWorkCorrectly()
    {
        // Arrange
        Logger.LogInformation("Testing service disposal and cleanup");
        var disposableServices = new List<IDisposable>();

        // Act - Create scoped services and track disposables
        for (int i = 0; i < 5; i++)
        {
            using var scope = ServiceProvider.CreateScope();

            // Get services that might be disposable
            var services = new object[]
            {
                scope.ServiceProvider.GetRequiredService<IFrameworkService>(),
                scope.ServiceProvider.GetRequiredService<IAbilityRegistry>(),
                scope.ServiceProvider.GetRequiredService<ICommandManager>()
            };

            foreach (var service in services)
            {
                if (service is IDisposable disposable)
                {
                    disposableServices.Add(disposable);
                }
            }
        }

        // Assert - Services should be created successfully
        Logger.LogInformation("Created {DisposableCount} disposable services during test", disposableServices.Count);

        // Act & Assert - Test that disposal doesn't throw exceptions
        foreach (var disposable in disposableServices.Take(3)) // Test a few to avoid performance issues
        {
            try
            {
                disposable.Dispose();
                Logger.LogDebug("Successfully disposed service of type {ServiceType}", disposable.GetType().Name);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to dispose service of type {ServiceType}", disposable.GetType().Name);
                throw;
            }
        }

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ServiceDisposalAndCleanup", TimeSpan.FromSeconds(1));

        Logger.LogInformation("Service disposal and cleanup test completed successfully");
    }

    /// <summary>
    /// Tests configuration binding and injection
    /// </summary>
    [Fact]
    [TestableMethod("ConfigurationBindingAndInjection", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    public async Task ConfigurationBinding_ShouldWorkCorrectly()
    {
        // Arrange
        Logger.LogInformation("Testing configuration binding and injection");

        // Act
        var configuration = ServiceProvider.GetRequiredService<IConfiguration>();

        // Assert
        configuration.Should().NotBeNull("Configuration should be injected");

        // Test specific configuration values
        var loggingLevel = configuration["Logging:LogLevel:Default"];
        loggingLevel.Should().NotBeNullOrEmpty("Logging configuration should be available");

        var databaseConnectionString = configuration["Database:ConnectionString"];
        databaseConnectionString.Should().NotBeNullOrEmpty("Database configuration should be available");

        Logger.LogInformation("Configuration values - Logging level: {LoggingLevel}, Database: {DatabaseConfig}",
            loggingLevel, databaseConnectionString);

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ConfigurationBindingAndInjection", TimeSpan.FromMilliseconds(500));

        Logger.LogInformation("Configuration binding and injection test completed successfully");
    }
}
