<Project>

  <!-- ArtDesignFramework MSBuild Dependency Validation Targets -->
  <!-- Last Updated: 2025-06-12 08:20:00 UTC -->
  <!-- Provides automated validation of project references and dependencies -->

  <PropertyGroup>
    <!-- Enable dependency validation by default -->
    <EnableDependencyValidation Condition="'$(EnableDependencyValidation)' == ''">true</EnableDependencyValidation>

    <!-- Validation settings -->
    <ValidateProjectReferences Condition="'$(ValidateProjectReferences)' == ''">true</ValidateProjectReferences>
    <FailOnInvalidReferences Condition="'$(FailOnInvalidReferences)' == ''">true</FailOnInvalidReferences>
    <VerboseDependencyLogging Condition="'$(VerboseDependencyLogging)' == ''">false</VerboseDependencyLogging>

    <!-- Circular dependency detection (disabled by default due to MSBuild integration issues) -->
    <EnableCircularDependencyDetection Condition="'$(EnableCircularDependencyDetection)' == ''">false</EnableCircularDependencyDetection>
  </PropertyGroup>

  <!-- Target to validate all project references before build -->
  <Target Name="ValidateProjectReferences"
          BeforeTargets="Build;Rebuild"
          Condition="'$(EnableDependencyValidation)' == 'true' AND '$(ValidateProjectReferences)' == 'true'">

    <Message Text="[ArtDesignFramework] Starting dependency validation for $(MSBuildProjectName)..."
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

    <!-- Validate each project reference -->
    <ItemGroup>
      <ProjectReferenceToValidate Include="@(ProjectReference)" />
    </ItemGroup>

    <!-- Check if referenced project files exist -->
    <ItemGroup>
      <ValidProjectReferences Include="@(ProjectReferenceToValidate)"
                              Condition="Exists('%(ProjectReferenceToValidate.Identity)')" />
      <MissingProjectFiles Include="@(ProjectReferenceToValidate)"
                           Condition="!Exists('%(ProjectReferenceToValidate.Identity)')" />
    </ItemGroup>

    <!-- Log validation results -->
    <Message Text="[ArtDesignFramework] Validating $(MSBuildProjectName): Found @(ProjectReferenceToValidate->Count()) project references"
             Importance="normal" />

    <Message Text="[ArtDesignFramework] Valid references: @(ValidProjectReferences->Count())"
             Importance="normal"
             Condition="'@(ValidProjectReferences)' != ''" />

    <Message Text="[ArtDesignFramework] ✓ Valid reference: %(ValidProjectReferences.Identity)"
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

    <!-- Report missing files -->
    <Warning Text="[ArtDesignFramework] ⚠ Missing project file: %(MissingProjectFiles.Identity)"
             Condition="'@(MissingProjectFiles)' != ''" />

    <!-- Fail build if invalid references found and FailOnInvalidReferences is true -->
    <Error Text="[ArtDesignFramework] ❌ Build failed: Invalid project references detected in $(MSBuildProjectName). Missing files: @(MissingProjectFiles, '; ')"
           Condition="'@(MissingProjectFiles)' != '' AND '$(FailOnInvalidReferences)' == 'true'" />

    <Message Text="[ArtDesignFramework] ✅ Dependency validation completed successfully for $(MSBuildProjectName)"
             Importance="normal" />

  </Target>

  <!-- Target to validate project reference integrity -->
  <Target Name="ValidateProjectReferenceIntegrity"
          BeforeTargets="Build;Rebuild"
          DependsOnTargets="ValidateProjectReferences"
          Condition="'$(EnableDependencyValidation)' == 'true'">

    <Message Text="[ArtDesignFramework] Validating project reference integrity..."
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

    <!-- Check for circular references (basic detection) -->
    <ItemGroup>
      <CurrentProjectPath Include="$(MSBuildProjectFile)" />
    </ItemGroup>

    <!-- Validate that references don't point to self -->
    <ItemGroup>
      <SelfReferences Include="@(ProjectReference)"
                      Condition="'%(ProjectReference.Identity)' == '$(MSBuildProjectFile)' OR
                                '%(ProjectReference.Filename)%(ProjectReference.Extension)' == '$(MSBuildProjectName).csproj'" />
    </ItemGroup>

    <Error Text="[ArtDesignFramework] ❌ Circular reference detected: $(MSBuildProjectName) references itself via %(SelfReferences.Identity)"
           Condition="'@(SelfReferences)' != ''" />

    <!-- Validate reference paths are relative and within expected structure -->
    <ItemGroup>
      <InvalidPathReferences Include="@(ProjectReference)"
                             Condition="!$([System.String]::new('%(ProjectReference.Identity)').StartsWith('..\'))" />
    </ItemGroup>

    <Warning Text="[ArtDesignFramework] ⚠ Potentially invalid reference path (should be relative): %(InvalidPathReferences.Identity)"
             Condition="'@(InvalidPathReferences)' != ''" />

    <Message Text="[ArtDesignFramework] ✅ Project reference integrity validation completed"
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

  </Target>

  <!-- Target to generate dependency report -->
  <Target Name="GenerateDependencyReport"
          AfterTargets="ValidateProjectReferenceIntegrity"
          Condition="'$(EnableDependencyValidation)' == 'true' AND '$(VerboseDependencyLogging)' == 'true'">

    <Message Text="[ArtDesignFramework] === Dependency Report for $(MSBuildProjectName) ==="
             Importance="high" />

    <Message Text="[ArtDesignFramework] Project: $(MSBuildProjectFile)"
             Importance="high" />

    <Message Text="[ArtDesignFramework] Target Framework: $(TargetFramework)"
             Importance="high" />

    <Message Text="[ArtDesignFramework] Configuration: $(Configuration)"
             Importance="high" />

    <Message Text="[ArtDesignFramework] Total Project References: @(ProjectReference->Count())"
             Importance="high" />

    <Message Text="[ArtDesignFramework] Reference: %(ProjectReference.Identity)"
             Importance="high"
             Condition="'@(ProjectReference)' != ''" />

    <Message Text="[ArtDesignFramework] === End Dependency Report ==="
             Importance="high" />

  </Target>

  <!-- Target to validate framework consistency -->
  <Target Name="ValidateFrameworkConsistency"
          BeforeTargets="Build"
          Condition="'$(EnableDependencyValidation)' == 'true'">

    <!-- Ensure all projects use the same target framework -->
    <Message Text="[ArtDesignFramework] Validating framework consistency: $(TargetFramework)"
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

    <!-- Check for framework mismatch (basic validation) -->
    <Error Text="[ArtDesignFramework] ❌ Framework mismatch: Expected net9.0, found $(TargetFramework)"
           Condition="'$(TargetFramework)' != 'net9.0'" />

    <Message Text="[ArtDesignFramework] ✅ Framework consistency validated: $(TargetFramework)"
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

  </Target>

  <!-- Target to clean validation cache -->
  <Target Name="CleanValidationCache"
          BeforeTargets="Clean">

    <Message Text="[ArtDesignFramework] Cleaning dependency validation cache..."
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

  </Target>

  <!-- Target to detect circular dependencies -->
  <Target Name="DetectCircularDependencies"
          BeforeTargets="Build;Rebuild"
          Condition="'$(EnableDependencyValidation)' == 'true' AND '$(EnableCircularDependencyDetection)' == 'true'">

    <Message Text="[ArtDesignFramework] Starting circular dependency detection..."
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

    <!-- Define dependency analyzer script paths -->
    <PropertyGroup>
      <DependencyAnalyzerBatch>$(MSBuildThisFileDirectory)..\build\DependencyAnalyzer.cmd</DependencyAnalyzerBatch>
      <DependencyAnalyzerScript>$(MSBuildThisFileDirectory)..\build\DependencyAnalyzer.ps1</DependencyAnalyzerScript>
      <SourceDirectory>$(MSBuildThisFileDirectory)</SourceDirectory>
    </PropertyGroup>

    <!-- Execute dependency cycle detection with simplified approach -->
    <PropertyGroup>
      <VerboseFlag Condition="'$(VerboseDependencyLogging)' == 'true'">-VerboseLogging</VerboseFlag>
      <VerboseFlag Condition="'$(VerboseDependencyLogging)' != 'true'"></VerboseFlag>
    </PropertyGroup>

    <!-- Run PowerShell script directly with error tolerance -->
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -NoProfile -File &quot;$(DependencyAnalyzerScript)&quot; -SourcePath &quot;$(SourceDirectory)&quot; $(VerboseFlag)"
          ContinueOnError="true"
          IgnoreExitCode="true"
          Condition="Exists('$(DependencyAnalyzerScript)')" >
      <Output TaskParameter="ExitCode" PropertyName="DependencyAnalysisExitCode" />
    </Exec>

    <!-- Only warn about dependency analysis issues, don't fail the build -->
    <Warning Text="[ArtDesignFramework] ⚠ Dependency cycle detection completed with exit code $(DependencyAnalysisExitCode). Check DependencyAnalysisReport.md for details."
             Condition="'$(DependencyAnalysisExitCode)' != '0' AND Exists('$(DependencyAnalyzerScript)')" />

    <Message Text="[ArtDesignFramework] ✅ Dependency cycle detection completed successfully"
             Importance="normal"
             Condition="'$(DependencyAnalysisExitCode)' == '0' AND Exists('$(DependencyAnalyzerScript)')" />

    <!-- Warning if script not found -->
    <Warning Text="[ArtDesignFramework] ⚠ Dependency analyzer script not found: $(DependencyAnalyzerScript)"
             Condition="!Exists('$(DependencyAnalyzerScript)')" />

    <Message Text="[ArtDesignFramework] ✅ Circular dependency detection completed"
             Importance="normal"
             Condition="'$(VerboseDependencyLogging)' == 'true'" />

  </Target>

  <!-- Property to allow disabling validation for specific projects -->
  <PropertyGroup Condition="'$(MSBuildProjectName)' == 'ArtDesignFramework.TestProject' OR
                            '$(MSBuildProjectName)' == 'ArtDesignFramework.Experimental'">
    <EnableDependencyValidation>false</EnableDependencyValidation>
  </PropertyGroup>

</Project>
