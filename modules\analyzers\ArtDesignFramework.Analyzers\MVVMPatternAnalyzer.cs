using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;
using System.Collections.Immutable;
using System.Linq;

namespace ArtDesignFramework.Analyzers
{
    /// <summary>
    /// Analyzer for validating MVVM pattern compliance in ArtDesignFramework.
    /// Last Updated: 2025-06-12 09:00:00 UTC
    /// Ensures proper ViewModel implementation, command patterns, and data binding practices.
    /// </summary>
    [DiagnosticAnalyzer(LanguageNames.CSharp)]
    public class MVVMPatternAnalyzer : DiagnosticAnalyzer
    {
        public static readonly DiagnosticDescriptor ViewModelMustImplementINotifyPropertyChangedRule = new DiagnosticDescriptor(
            "ADF2001",
            "ViewModel must implement INotifyPropertyChanged",
            "ViewModel '{0}' must implement INotifyPropertyChanged interface",
            "MVVM",
            DiagnosticSeverity.Error,
            isEnabledByDefault: true,
            description: "All ViewModels must implement INotifyPropertyChanged to support proper data binding.");

        public static readonly DiagnosticDescriptor PropertyShouldRaisePropertyChangedRule = new DiagnosticDescriptor(
            "ADF2002",
            "Property should raise PropertyChanged event",
            "Property '{0}' in ViewModel '{1}' should raise PropertyChanged event in setter",
            "MVVM",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "ViewModel properties should raise PropertyChanged events to notify the UI of changes.");

        public static readonly DiagnosticDescriptor CommandShouldImplementICommandRule = new DiagnosticDescriptor(
            "ADF2003",
            "Command property should implement ICommand",
            "Command property '{0}' should implement ICommand interface",
            "MVVM",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "Command properties in ViewModels should implement ICommand interface.");

        public static readonly DiagnosticDescriptor ViewModelShouldNotReferenceUIRule = new DiagnosticDescriptor(
            "ADF2004",
            "ViewModel should not reference UI elements",
            "ViewModel '{0}' should not directly reference UI elements like '{1}'",
            "MVVM",
            DiagnosticSeverity.Error,
            isEnabledByDefault: true,
            description: "ViewModels should not have direct references to UI elements to maintain separation of concerns.");

        public static readonly DiagnosticDescriptor ViewModelShouldBeTestableRule = new DiagnosticDescriptor(
            "ADF2005",
            "ViewModel should be testable",
            "ViewModel '{0}' should have [TestableClass] attribute and testable constructor",
            "MVVM",
            DiagnosticSeverity.Info,
            isEnabledByDefault: true,
            description: "ViewModels should be designed for testability with proper attributes and dependency injection.");

        public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics =>
            ImmutableArray.Create(
                ViewModelMustImplementINotifyPropertyChangedRule,
                PropertyShouldRaisePropertyChangedRule,
                CommandShouldImplementICommandRule,
                ViewModelShouldNotReferenceUIRule,
                ViewModelShouldBeTestableRule);

        public override void Initialize(AnalysisContext context)
        {
            context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
            context.EnableConcurrentExecution();
            context.RegisterSyntaxNodeAction(AnalyzeClassDeclaration, SyntaxKind.ClassDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzePropertyDeclaration, SyntaxKind.PropertyDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzeFieldDeclaration, SyntaxKind.FieldDeclaration);
        }

        private static void AnalyzeClassDeclaration(SyntaxNodeAnalysisContext context)
        {
            var classDeclaration = (ClassDeclarationSyntax)context.Node;
            var className = classDeclaration.Identifier.ValueText;

            // Only analyze ViewModels
            if (!className.EndsWith("ViewModel"))
                return;

            var semanticModel = context.SemanticModel;
            var classSymbol = semanticModel.GetDeclaredSymbol(classDeclaration);

            if (classSymbol != null)
            {
                CheckINotifyPropertyChangedImplementation(context, classDeclaration, classSymbol, className);
                CheckViewModelTestability(context, classDeclaration, className);
                CheckUIElementReferences(context, classDeclaration, classSymbol, className);
            }
        }

        private static void AnalyzePropertyDeclaration(SyntaxNodeAnalysisContext context)
        {
            var propertyDeclaration = (PropertyDeclarationSyntax)context.Node;
            var containingClass = propertyDeclaration.Ancestors().OfType<ClassDeclarationSyntax>().FirstOrDefault();

            if (containingClass == null || !containingClass.Identifier.ValueText.EndsWith("ViewModel"))
                return;

            var propertyName = propertyDeclaration.Identifier.ValueText;
            var className = containingClass.Identifier.ValueText;

            CheckPropertyChangedImplementation(context, propertyDeclaration, propertyName, className);
            CheckCommandImplementation(context, propertyDeclaration, propertyName);
        }

        private static void AnalyzeFieldDeclaration(SyntaxNodeAnalysisContext context)
        {
            var fieldDeclaration = (FieldDeclarationSyntax)context.Node;
            var containingClass = fieldDeclaration.Ancestors().OfType<ClassDeclarationSyntax>().FirstOrDefault();

            if (containingClass == null || !containingClass.Identifier.ValueText.EndsWith("ViewModel"))
                return;

            var className = containingClass.Identifier.ValueText;
            CheckUIElementReferencesInFields(context, fieldDeclaration, className);
        }

        private static void CheckINotifyPropertyChangedImplementation(
            SyntaxNodeAnalysisContext context,
            ClassDeclarationSyntax classDeclaration,
            INamedTypeSymbol classSymbol,
            string className)
        {
            var implementsINotifyPropertyChanged = classSymbol.AllInterfaces
                .Any(i => i.Name == "INotifyPropertyChanged");

            if (!implementsINotifyPropertyChanged)
            {
                var diagnostic = Diagnostic.Create(
                    ViewModelMustImplementINotifyPropertyChangedRule,
                    classDeclaration.Identifier.GetLocation(),
                    className);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckPropertyChangedImplementation(
            SyntaxNodeAnalysisContext context,
            PropertyDeclarationSyntax propertyDeclaration,
            string propertyName,
            string className)
        {
            // Skip auto-implemented properties and read-only properties
            if (propertyDeclaration.AccessorList == null)
                return;

            var setter = propertyDeclaration.AccessorList.Accessors
                .FirstOrDefault(a => a.Keyword.IsKind(SyntaxKind.SetKeyword));

            if (setter?.Body == null && setter?.ExpressionBody == null)
                return;

            // Check if setter raises PropertyChanged event
            var setterText = setter.Body?.ToString() ?? setter.ExpressionBody?.ToString() ?? "";
            
            if (!setterText.Contains("PropertyChanged") && 
                !setterText.Contains("OnPropertyChanged") &&
                !setterText.Contains("RaisePropertyChanged") &&
                !setterText.Contains("NotifyPropertyChanged"))
            {
                var diagnostic = Diagnostic.Create(
                    PropertyShouldRaisePropertyChangedRule,
                    propertyDeclaration.Identifier.GetLocation(),
                    propertyName,
                    className);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckCommandImplementation(
            SyntaxNodeAnalysisContext context,
            PropertyDeclarationSyntax propertyDeclaration,
            string propertyName)
        {
            // Check if property name suggests it's a command
            if (propertyName.EndsWith("Command"))
            {
                var propertyType = propertyDeclaration.Type.ToString();
                
                if (!propertyType.Contains("ICommand") && 
                    !propertyType.Contains("RelayCommand") &&
                    !propertyType.Contains("DelegateCommand"))
                {
                    var diagnostic = Diagnostic.Create(
                        CommandShouldImplementICommandRule,
                        propertyDeclaration.Identifier.GetLocation(),
                        propertyName);
                    context.ReportDiagnostic(diagnostic);
                }
            }
        }

        private static void CheckViewModelTestability(
            SyntaxNodeAnalysisContext context,
            ClassDeclarationSyntax classDeclaration,
            string className)
        {
            var hasTestableAttribute = classDeclaration.AttributeLists
                .SelectMany(al => al.Attributes)
                .Any(attr => attr.Name.ToString().Contains("TestableClass") || 
                           attr.Name.ToString().Contains("Testable"));

            if (!hasTestableAttribute)
            {
                var diagnostic = Diagnostic.Create(
                    ViewModelShouldBeTestableRule,
                    classDeclaration.Identifier.GetLocation(),
                    className);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckUIElementReferences(
            SyntaxNodeAnalysisContext context,
            ClassDeclarationSyntax classDeclaration,
            INamedTypeSymbol classSymbol,
            string className)
        {
            // Check for UI element references in using statements
            var usingDirectives = classDeclaration.SyntaxTree.GetRoot()
                .DescendantNodes().OfType<UsingDirectiveSyntax>();

            foreach (var usingDirective in usingDirectives)
            {
                var namespaceName = usingDirective.Name?.ToString();
                if (IsUINamespace(namespaceName))
                {
                    var diagnostic = Diagnostic.Create(
                        ViewModelShouldNotReferenceUIRule,
                        usingDirective.GetLocation(),
                        className,
                        namespaceName);
                    context.ReportDiagnostic(diagnostic);
                }
            }
        }

        private static void CheckUIElementReferencesInFields(
            SyntaxNodeAnalysisContext context,
            FieldDeclarationSyntax fieldDeclaration,
            string className)
        {
            var fieldType = fieldDeclaration.Declaration.Type.ToString();
            
            if (IsUIType(fieldType))
            {
                var diagnostic = Diagnostic.Create(
                    ViewModelShouldNotReferenceUIRule,
                    fieldDeclaration.GetLocation(),
                    className,
                    fieldType);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static bool IsUINamespace(string? namespaceName)
        {
            if (string.IsNullOrEmpty(namespaceName))
                return false;

            return namespaceName.Contains("System.Windows") ||
                   namespaceName.Contains("Microsoft.UI") ||
                   namespaceName.Contains("Windows.UI") ||
                   namespaceName.Contains("Avalonia") ||
                   namespaceName.Contains("Xamarin.Forms") ||
                   namespaceName.Contains("Maui");
        }

        private static bool IsUIType(string typeName)
        {
            return typeName.Contains("Button") ||
                   typeName.Contains("TextBox") ||
                   typeName.Contains("Label") ||
                   typeName.Contains("Panel") ||
                   typeName.Contains("Control") ||
                   typeName.Contains("Window") ||
                   typeName.Contains("Page") ||
                   typeName.Contains("UserControl");
        }
    }
}
