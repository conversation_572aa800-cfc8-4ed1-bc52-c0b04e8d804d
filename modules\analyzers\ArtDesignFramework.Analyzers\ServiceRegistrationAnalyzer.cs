using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;
using System.Collections.Immutable;
using System.Linq;

namespace ArtDesignFramework.Analyzers
{
    /// <summary>
    /// Analyzer for validating service registration patterns in ArtDesignFramework.
    /// Last Updated: 2025-06-12 08:55:00 UTC
    /// Ensures proper dependency injection patterns and service lifetime management.
    /// </summary>
    [DiagnosticAnalyzer(LanguageNames.CSharp)]
    public class ServiceRegistrationAnalyzer : DiagnosticAnalyzer
    {
        public static readonly DiagnosticDescriptor MissingServiceExtensionRule = new DiagnosticDescriptor(
            "ADF1001",
            "Module should have ServiceCollectionExtensions class",
            "Module '{0}' should have a ServiceCollectionExtensions class for proper service registration",
            "ServiceRegistration",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "Each module should provide a ServiceCollectionExtensions class for dependency injection setup.");

        public static readonly DiagnosticDescriptor InvalidServiceLifetimeRule = new DiagnosticDescriptor(
            "ADF1002",
            "Service registration uses inappropriate lifetime",
            "Service '{0}' registration should use appropriate lifetime (Singleton/Scoped/Transient)",
            "ServiceRegistration",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "Services should be registered with appropriate lifetimes based on their usage patterns.");

        public static readonly DiagnosticDescriptor MissingInterfaceRegistrationRule = new DiagnosticDescriptor(
            "ADF1003",
            "Service should be registered by interface",
            "Service '{0}' should be registered by its interface for better testability",
            "ServiceRegistration",
            DiagnosticSeverity.Info,
            isEnabledByDefault: true,
            description: "Services should be registered by their interfaces to enable proper dependency injection and testing.");

        public static readonly DiagnosticDescriptor InvalidExtensionMethodNameRule = new DiagnosticDescriptor(
            "ADF1004",
            "Service extension method should follow naming convention",
            "Extension method '{0}' should follow 'Add{ModuleName}' naming convention",
            "ServiceRegistration",
            DiagnosticSeverity.Info,
            isEnabledByDefault: true,
            description: "Service registration extension methods should follow consistent naming patterns.");

        public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics =>
            ImmutableArray.Create(
                MissingServiceExtensionRule,
                InvalidServiceLifetimeRule,
                MissingInterfaceRegistrationRule,
                InvalidExtensionMethodNameRule);

        public override void Initialize(AnalysisContext context)
        {
            context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
            context.EnableConcurrentExecution();
            context.RegisterSyntaxNodeAction(AnalyzeMethodDeclaration, SyntaxKind.MethodDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzeInvocationExpression, SyntaxKind.InvocationExpression);
            context.RegisterCompilationAction(AnalyzeCompilation);
        }

        private static void AnalyzeMethodDeclaration(SyntaxNodeAnalysisContext context)
        {
            var methodDeclaration = (MethodDeclarationSyntax)context.Node;
            
            // Check if this is a service registration extension method
            if (IsServiceRegistrationMethod(methodDeclaration))
            {
                CheckExtensionMethodNaming(context, methodDeclaration);
                CheckServiceRegistrationPatterns(context, methodDeclaration);
            }
        }

        private static void AnalyzeInvocationExpression(SyntaxNodeAnalysisContext context)
        {
            var invocation = (InvocationExpressionSyntax)context.Node;
            
            // Check service registration calls
            if (IsServiceRegistrationCall(invocation))
            {
                CheckServiceLifetime(context, invocation);
                CheckInterfaceRegistration(context, invocation);
            }
        }

        private static void AnalyzeCompilation(CompilationAnalysisContext context)
        {
            // Check if modules have ServiceCollectionExtensions
            CheckForMissingServiceExtensions(context);
        }

        private static bool IsServiceRegistrationMethod(MethodDeclarationSyntax method)
        {
            return method.Modifiers.Any(SyntaxKind.PublicKeyword) &&
                   method.Modifiers.Any(SyntaxKind.StaticKeyword) &&
                   method.ParameterList.Parameters.Count > 0 &&
                   method.ParameterList.Parameters[0].Type?.ToString().Contains("IServiceCollection") == true;
        }

        private static bool IsServiceRegistrationCall(InvocationExpressionSyntax invocation)
        {
            var memberAccess = invocation.Expression as MemberAccessExpressionSyntax;
            if (memberAccess == null) return false;

            var methodName = memberAccess.Name.Identifier.ValueText;
            return methodName.StartsWith("Add") && 
                   (methodName.Contains("Singleton") || methodName.Contains("Scoped") || methodName.Contains("Transient"));
        }

        private static void CheckExtensionMethodNaming(SyntaxNodeAnalysisContext context, MethodDeclarationSyntax method)
        {
            var methodName = method.Identifier.ValueText;
            
            if (!methodName.StartsWith("Add"))
            {
                var diagnostic = Diagnostic.Create(
                    InvalidExtensionMethodNameRule,
                    method.Identifier.GetLocation(),
                    methodName);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckServiceRegistrationPatterns(SyntaxNodeAnalysisContext context, MethodDeclarationSyntax method)
        {
            var methodBody = method.Body;
            if (methodBody == null) return;

            // Check if method contains proper service registrations
            var hasServiceRegistration = methodBody.Statements
                .OfType<ExpressionStatementSyntax>()
                .Any(stmt => stmt.Expression.ToString().Contains("services.Add"));

            if (!hasServiceRegistration)
            {
                var diagnostic = Diagnostic.Create(
                    MissingServiceExtensionRule,
                    method.Identifier.GetLocation(),
                    method.Identifier.ValueText);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckServiceLifetime(SyntaxNodeAnalysisContext context, InvocationExpressionSyntax invocation)
        {
            var memberAccess = invocation.Expression as MemberAccessExpressionSyntax;
            if (memberAccess == null) return;

            var methodName = memberAccess.Name.Identifier.ValueText;
            var arguments = invocation.ArgumentList.Arguments;

            // Basic heuristics for appropriate lifetimes
            if (arguments.Count >= 2)
            {
                var serviceType = arguments[0].ToString();
                var implementationType = arguments[1].ToString();

                // Check for potential singleton services that should be scoped
                if (methodName.Contains("Singleton") && 
                    (serviceType.Contains("Repository") || serviceType.Contains("Context")))
                {
                    var diagnostic = Diagnostic.Create(
                        InvalidServiceLifetimeRule,
                        invocation.GetLocation(),
                        serviceType);
                    context.ReportDiagnostic(diagnostic);
                }
            }
        }

        private static void CheckInterfaceRegistration(SyntaxNodeAnalysisContext context, InvocationExpressionSyntax invocation)
        {
            var arguments = invocation.ArgumentList.Arguments;
            
            if (arguments.Count >= 2)
            {
                var serviceType = arguments[0].ToString();
                var implementationType = arguments[1].ToString();

                // Check if registering concrete type instead of interface
                if (!serviceType.StartsWith("I") || serviceType == implementationType)
                {
                    var diagnostic = Diagnostic.Create(
                        MissingInterfaceRegistrationRule,
                        invocation.GetLocation(),
                        serviceType);
                    context.ReportDiagnostic(diagnostic);
                }
            }
        }

        private static void CheckForMissingServiceExtensions(CompilationAnalysisContext context)
        {
            var compilation = context.Compilation;
            var assemblyName = compilation.AssemblyName;

            // Skip if this is not a framework module
            if (assemblyName == null || !assemblyName.StartsWith("ArtDesignFramework."))
            {
                return;
            }

            // Check if assembly contains ServiceCollectionExtensions
            var hasServiceExtensions = compilation.GetSymbolsWithName("ServiceCollectionExtensions")
                .Any(symbol => symbol is INamedTypeSymbol);

            if (!hasServiceExtensions)
            {
                // Report diagnostic for missing service extensions
                var diagnostic = Diagnostic.Create(
                    MissingServiceExtensionRule,
                    Location.None,
                    assemblyName);
                context.ReportDiagnostic(diagnostic);
            }
        }
    }
}
