<Project>

  <!-- Isolated Directory.Build.props for Consolidated ArtDesignFramework Modules -->
  <!-- This file prevents inheritance from parent directories and provides unified .NET 9.0 configuration -->

  <PropertyGroup>
    <!-- Framework and Language Settings -->
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- Documentation and Code Quality -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>

    <!-- Assembly Information -->
    <Company>ArtDesignFramework</Company>
    <Product>ArtDesignFramework</Product>
    <Copyright>Copyright © ArtDesignFramework 2025</Copyright>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
    <PackageVersion>1.0.0</PackageVersion>

    <!-- Build Configuration -->
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>

    <!-- Code Analysis -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)ArtDesignFramework.ruleset</CodeAnalysisRuleSet>

    <!-- Performance -->
    <Optimize Condition="'$(Configuration)' == 'Release'">true</Optimize>
    <DebugType Condition="'$(Configuration)' == 'Release'">portable</DebugType>
    <DebugType Condition="'$(Configuration)' == 'Debug'">full</DebugType>

    <!-- Package Information -->
    <PackageProjectUrl>https://github.com/ArtDesignFramework/Framework</PackageProjectUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <RepositoryUrl>https://github.com/ArtDesignFramework/Framework</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
  </PropertyGroup>

  <!-- Common Package References for All Modules -->
  <ItemGroup>
    <!-- Core .NET packages with consistent versions -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

  <!-- Graphics-related Package References (exclude Performance and Utilities) -->
  <ItemGroup Condition="'$(MSBuildProjectName)' != 'ArtDesignFramework.Performance' AND '$(MSBuildProjectName)' != 'ArtDesignFramework.Utilities'">
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
  </ItemGroup>

  <!-- Test Project Configuration -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <!-- Test Package References -->
  <ItemGroup Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NUnit" Version="4.0.1" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
  </ItemGroup>

  <!-- ArtDesignFramework Architectural Compliance Analyzers -->
  <!-- Last Updated: 2025-06-12 09:10:00 UTC -->
  <ItemGroup Condition="'$(MSBuildProjectName)' != 'ArtDesignFramework.Analyzers'">
    <Analyzer Include="$(MSBuildThisFileDirectory)..\analyzers\ArtDesignFramework.Analyzers\bin\$(Configuration)\netstandard2.0\ArtDesignFramework.Analyzers.dll"
              Condition="Exists('$(MSBuildThisFileDirectory)..\analyzers\ArtDesignFramework.Analyzers\bin\$(Configuration)\netstandard2.0\ArtDesignFramework.Analyzers.dll')" />
  </ItemGroup>

  <!-- Analyzer Configuration -->
  <PropertyGroup>
    <!-- Enable architectural compliance checking -->
    <EnableArchitecturalCompliance Condition="'$(EnableArchitecturalCompliance)' == ''">true</EnableArchitecturalCompliance>

    <!-- Configure analyzer severity levels -->
    <WarningsAsErrors Condition="'$(EnableArchitecturalCompliance)' == 'true'">$(WarningsAsErrors);ADF0003;ADF2001;ADF2004</WarningsAsErrors>
    <WarningsNotAsErrors Condition="'$(EnableArchitecturalCompliance)' == 'true'">$(WarningsNotAsErrors);ADF0001;ADF0002;ADF1001;ADF2002;ADF2005</WarningsNotAsErrors>
  </PropertyGroup>

</Project>
