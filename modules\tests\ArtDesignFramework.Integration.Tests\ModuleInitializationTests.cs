// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;
using ArtDesignFramework.Core;
using ArtDesignFramework.Integration.Tests.Infrastructure;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace ArtDesignFramework.Integration.Tests;

/// <summary>
/// Integration tests for module initialization and service registration
/// Last Updated: 2025-01-27 22:45:00 UTC
/// </summary>
public class ModuleInitializationTests : IntegrationTestBase
{
    public ModuleInitializationTests(ITestOutputHelper output) : base(output)
    {
    }

    /// <summary>
    /// Tests that all framework modules initialize correctly
    /// </summary>
    [Fact]
    [TestableMethod("AllModulesInitialization", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public async Task AllModules_ShouldInitializeCorrectly()
    {
        // Arrange
        Logger.LogInformation("Starting comprehensive module initialization test");

        // Act & Assert - Validate framework initialization
        await ValidateFrameworkInitializationAsync();

        // Act & Assert - Validate service registration
        await ValidateServiceRegistrationAsync();

        // Act & Assert - Test core module initialization
        await ValidateCoreModuleAsync();

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("AllModulesInitialization", TimeSpan.FromSeconds(5));

        Logger.LogInformation("All modules initialized successfully");
    }

    /// <summary>
    /// Validates Core module initialization
    /// </summary>
    [TestableMethod("CoreModuleValidation", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    private async Task ValidateCoreModuleAsync()
    {
        Logger.LogInformation("Validating Core module");

        // Validate core services
        var frameworkService = ServiceProvider.GetRequiredService<IFrameworkService>();
        frameworkService.Should().NotBeNull();
        frameworkService.IsInitialized.Should().BeTrue();

        var abilityRegistry = ServiceProvider.GetRequiredService<IAbilityRegistry>();
        abilityRegistry.Should().NotBeNull();

        var commandManager = ServiceProvider.GetRequiredService<ICommandManager>();
        commandManager.Should().NotBeNull();

        var layerManager = ServiceProvider.GetRequiredService<ILayerManager>();
        layerManager.Should().NotBeNull();

        Logger.LogInformation("Core module validation completed");
        await Task.CompletedTask;
    }







    /// <summary>
    /// Tests module dependency resolution
    /// </summary>
    [Fact]
    [TestableMethod("ModuleDependencyResolution", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task ModuleDependencies_ShouldResolveCorrectly()
    {
        // Arrange
        Logger.LogInformation("Testing module dependency resolution");

        // Act & Assert - Test that modules can resolve their dependencies
        var dependencyTests = new[]
        {
            () => ServiceProvider.GetRequiredService<IFrameworkService>(),
            () => ServiceProvider.GetRequiredService<IAbilityRegistry>(),
            () => ServiceProvider.GetRequiredService<ICommandManager>(),
            () => ServiceProvider.GetRequiredService<ILayerManager>()
        };

        foreach (var test in dependencyTests)
        {
            var service = test();
            service.Should().NotBeNull("All dependencies should resolve correctly");
        }

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ModuleDependencyResolution", TimeSpan.FromSeconds(2));

        Logger.LogInformation("Module dependency resolution test completed successfully");
    }

    /// <summary>
    /// Tests service lifetime management
    /// </summary>
    [Fact]
    [TestableMethod("ServiceLifetimeManagement", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task ServiceLifetimes_ShouldBeConfiguredCorrectly()
    {
        // Arrange
        Logger.LogInformation("Testing service lifetime management");

        // Act & Assert - Test singleton services
        var frameworkService1 = ServiceProvider.GetRequiredService<IFrameworkService>();
        var frameworkService2 = ServiceProvider.GetRequiredService<IFrameworkService>();

        frameworkService1.Should().BeSameAs(frameworkService2, "Framework service should be singleton");

        // Act & Assert - Test scoped services (if any)
        using var scope1 = ServiceProvider.CreateScope();
        using var scope2 = ServiceProvider.CreateScope();

        // Act & Assert - Validate performance
        await ValidatePerformanceAsync("ServiceLifetimeManagement", TimeSpan.FromSeconds(1));

        Logger.LogInformation("Service lifetime management test completed successfully");
    }


}
