// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;
using ArtDesignFramework.Abstractions;
using ArtDesignFramework.HealthMonitoring.Core;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.HealthMonitoring.Services;

/// <summary>
/// Automated integrity monitoring service that continuously validates framework architectural integrity
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class AutomatedIntegrityMonitor : BackgroundService, IModuleHealthMonitor
{
    private readonly ILogger<AutomatedIntegrityMonitor> _logger;
    private readonly HealthMonitoringOptions _options;
    private readonly IServiceProvider _serviceProvider;

    private readonly ConcurrentDictionary<string, ModuleHealth> _moduleHealthCache = new();
    private readonly ConcurrentDictionary<string, List<ModuleHealth>> _healthHistory = new();
    private readonly ConcurrentDictionary<string, Dictionary<string, Func<CancellationToken, Task<HealthCheckResult>>>> _customHealthChecks = new();
    private readonly ConcurrentDictionary<string, HealthAlert> _activeAlerts = new();

    private readonly Timer _healthCheckTimer;
    private readonly Timer _alertProcessingTimer;
    private readonly Timer _cleanupTimer;

    private bool _isMonitoring;
    private readonly object _monitoringLock = new();

    /// <summary>
    /// Initializes a new instance of the AutomatedIntegrityMonitor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="options">Health monitoring options</param>
    /// <param name="serviceProvider">Service provider for dependency resolution</param>
    public AutomatedIntegrityMonitor(
        ILogger<AutomatedIntegrityMonitor> logger,
        IOptions<HealthMonitoringOptions> options,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        // Initialize timers
        _healthCheckTimer = new Timer(PerformScheduledHealthChecks, null, Timeout.Infinite, Timeout.Infinite);
        _alertProcessingTimer = new Timer(ProcessAlerts, null, Timeout.Infinite, Timeout.Infinite);
        _cleanupTimer = new Timer(PerformCleanup, null, Timeout.Infinite, Timeout.Infinite);

        _logger.LogInformation("AutomatedIntegrityMonitor initialized with check interval: {Interval}", _options.CheckInterval);
    }

    /// <summary>
    /// Event raised when a health alert is triggered
    /// </summary>
    public event EventHandler<HealthAlert>? AlertTriggered;

    /// <summary>
    /// Event raised when module health changes
    /// </summary>
    public event EventHandler<ModuleHealth>? HealthChanged;

    /// <summary>
    /// Event raised when a health check completes
    /// </summary>
    public event EventHandler<(string ModuleName, ModuleHealth Health)>? HealthCheckCompleted;

    /// <summary>
    /// Starts the background monitoring service
    /// </summary>
    /// <param name="stoppingToken">Cancellation token</param>
    /// <returns>Task representing the background operation</returns>
    [TestableMethod("ExecuteAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting automated integrity monitoring service");

        try
        {
            await StartMonitoringAsync(stoppingToken);

            // Keep the service running until cancellation is requested
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Automated integrity monitoring service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in automated integrity monitoring service");
            throw;
        }
        finally
        {
            await StopMonitoringAsync(CancellationToken.None);
        }
    }

    /// <summary>
    /// Starts health monitoring for all configured modules
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the start operation</returns>
    [TestableMethod("StartMonitoringAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        lock (_monitoringLock)
        {
            if (_isMonitoring)
            {
                _logger.LogWarning("Monitoring is already started");
                return;
            }

            _isMonitoring = true;
        }

        _logger.LogInformation("Starting health monitoring for {ModuleCount} modules", _options.ModulesToMonitor.Count);

        try
        {
            // Perform initial health check
            await CheckAllModulesHealthAsync(cancellationToken);

            // Start periodic timers
            var checkInterval = _options.CheckInterval;
            _healthCheckTimer.Change(checkInterval, checkInterval);
            _alertProcessingTimer.Change(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            _cleanupTimer.Change(TimeSpan.FromHours(1), TimeSpan.FromHours(1));

            _logger.LogInformation("Health monitoring started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start health monitoring");
            lock (_monitoringLock)
            {
                _isMonitoring = false;
            }
            throw;
        }
    }

    /// <summary>
    /// Stops health monitoring
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the stop operation</returns>
    [TestableMethod("StopMonitoringAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        lock (_monitoringLock)
        {
            if (!_isMonitoring)
            {
                return;
            }

            _isMonitoring = false;
        }

        _logger.LogInformation("Stopping health monitoring");

        try
        {
            // Stop timers
            await _healthCheckTimer.DisposeAsync();
            await _alertProcessingTimer.DisposeAsync();
            await _cleanupTimer.DisposeAsync();

            _logger.LogInformation("Health monitoring stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping health monitoring");
            throw;
        }
    }

    /// <summary>
    /// Performs a health check on a specific module
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Module health information</returns>
    [TestableMethod("CheckModuleHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public async Task<ModuleHealth> CheckModuleHealthAsync(string moduleName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(moduleName))
            throw new ArgumentException("Module name cannot be null or empty", nameof(moduleName));

        var stopwatch = Stopwatch.StartNew();
        _logger.LogDebug("Starting health check for module: {ModuleName}", moduleName);

        try
        {
            var moduleHealth = new ModuleHealth
            {
                ModuleName = moduleName,
                LastChecked = DateTime.UtcNow,
                Version = GetModuleVersion(moduleName)
            };

            // Perform core health checks
            var healthChecks = new List<Task<HealthCheckResult>>
            {
                CheckModuleCompilationAsync(moduleName, cancellationToken),
                CheckModuleDependenciesAsync(moduleName, cancellationToken),
                CheckArchitecturalComplianceAsync(moduleName, cancellationToken),
                CheckPerformanceMetricsAsync(moduleName, cancellationToken),
                CheckServiceRegistrationAsync(moduleName, cancellationToken)
            };

            // Add custom health checks if any
            if (_customHealthChecks.TryGetValue(moduleName, out var customChecks))
            {
                foreach (var customCheck in customChecks.Values)
                {
                    healthChecks.Add(customCheck(cancellationToken));
                }
            }

            // Execute all health checks
            var results = await Task.WhenAll(healthChecks);
            moduleHealth.CheckResults.AddRange(results);

            // Calculate overall health score and status
            CalculateHealthScore(moduleHealth);

            stopwatch.Stop();
            moduleHealth.CheckDuration = stopwatch.Elapsed;

            // Update cache and history
            _moduleHealthCache.AddOrUpdate(moduleName, moduleHealth, (key, oldValue) => moduleHealth);
            UpdateHealthHistory(moduleName, moduleHealth);

            // Check for alerts
            await ProcessModuleAlertsAsync(moduleHealth, cancellationToken);

            // Raise events
            HealthCheckCompleted?.Invoke(this, (moduleName, moduleHealth));

            var previousHealth = _moduleHealthCache.GetValueOrDefault(moduleName);
            if (previousHealth?.Status != moduleHealth.Status)
            {
                HealthChanged?.Invoke(this, moduleHealth);
            }

            _logger.LogInformation("Health check completed for {ModuleName}: {Status} (Score: {Score:F2})",
                moduleName, moduleHealth.Status, moduleHealth.HealthScore);

            return moduleHealth;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for module: {ModuleName}", moduleName);

            var errorHealth = new ModuleHealth
            {
                ModuleName = moduleName,
                Status = ModuleHealthStatus.Critical,
                HealthScore = 0.0,
                LastChecked = DateTime.UtcNow,
                CheckDuration = stopwatch.Elapsed,
                Errors = { $"Health check failed: {ex.Message}" }
            };

            return errorHealth;
        }
    }

    /// <summary>
    /// Performs health checks on all monitored modules
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health information for all modules</returns>
    [TestableMethod("CheckAllModulesHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 30000)]
    public async Task<Dictionary<string, ModuleHealth>> CheckAllModulesHealthAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting health check for all {ModuleCount} modules", _options.ModulesToMonitor.Count);

        var healthTasks = _options.ModulesToMonitor
            .Select(moduleName => CheckModuleHealthAsync(moduleName, cancellationToken))
            .ToArray();

        try
        {
            var healthResults = await Task.WhenAll(healthTasks);
            var healthDictionary = healthResults.ToDictionary(h => h.ModuleName, h => h);

            _logger.LogInformation("Completed health check for all modules. Healthy: {HealthyCount}, Issues: {IssueCount}",
                healthResults.Count(h => h.Status == ModuleHealthStatus.Healthy),
                healthResults.Count(h => h.Status != ModuleHealthStatus.Healthy));

            return healthDictionary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for all modules");
            throw;
        }
    }

    /// <summary>
    /// Performs scheduled health checks
    /// </summary>
    /// <param name="state">Timer state</param>
    private async void PerformScheduledHealthChecks(object? state)
    {
        if (!_isMonitoring)
            return;

        try
        {
            _logger.LogDebug("Performing scheduled health checks");
            await CheckAllModulesHealthAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in scheduled health checks");
        }
    }

    /// <summary>
    /// Processes alerts for active monitoring
    /// </summary>
    /// <param name="state">Timer state</param>
    private async void ProcessAlerts(object? state)
    {
        if (!_isMonitoring)
            return;

        try
        {
            _logger.LogDebug("Processing health alerts");

            // Process any pending alerts
            var activeAlerts = _activeAlerts.Values.Where(a => a.IsActive).ToList();

            foreach (var alert in activeAlerts)
            {
                // Check if alert conditions still exist
                if (await ShouldMaintainAlertAsync(alert))
                {
                    _logger.LogWarning("Alert {AlertId} remains active: {Message}", alert.Id, alert.Message);
                }
                else
                {
                    // Deactivate resolved alerts
                    alert.IsActive = false;
                    _logger.LogInformation("Alert {AlertId} has been resolved", alert.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing alerts");
        }
    }

    /// <summary>
    /// Performs cleanup of old data
    /// </summary>
    /// <param name="state">Timer state</param>
    private void PerformCleanup(object? state)
    {
        if (!_isMonitoring)
            return;

        try
        {
            _logger.LogDebug("Performing health data cleanup");

            var cutoffTime = DateTime.UtcNow - _options.HealthRecordRetention;
            var totalRemoved = 0;

            foreach (var moduleName in _healthHistory.Keys.ToList())
            {
                if (_healthHistory.TryGetValue(moduleName, out var history))
                {
                    var originalCount = history.Count;
                    history.RemoveAll(h => h.LastChecked < cutoffTime);

                    // Also limit by max records
                    if (history.Count > _options.MaxHealthRecords)
                    {
                        var excess = history.Count - _options.MaxHealthRecords;
                        history.RemoveRange(0, excess);
                    }

                    totalRemoved += originalCount - history.Count;
                }
            }

            // Clean up resolved alerts older than retention period
            var alertsToRemove = _activeAlerts.Values
                .Where(a => !a.IsActive && a.Timestamp < cutoffTime)
                .Select(a => a.Id)
                .ToList();

            foreach (var alertId in alertsToRemove)
            {
                _activeAlerts.TryRemove(alertId, out _);
            }

            if (totalRemoved > 0 || alertsToRemove.Count > 0)
            {
                _logger.LogInformation("Cleanup completed: {HealthRecordsRemoved} health records, {AlertsRemoved} alerts removed",
                    totalRemoved, alertsToRemove.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cleanup");
        }
    }

    /// <summary>
    /// Checks module compilation status
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    private async Task<HealthCheckResult> CheckModuleCompilationAsync(string moduleName, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath) || !File.Exists(projectPath))
            {
                return new HealthCheckResult
                {
                    CheckName = "Compilation",
                    Description = "Module compilation status",
                    IsHealthy = false,
                    Severity = HealthCheckSeverity.Error,
                    Message = $"Project file not found: {projectPath}",
                    Duration = stopwatch.Elapsed
                };
            }

            // Run dotnet build to check compilation
            var buildProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"build \"{projectPath}\" --no-restore --verbosity quiet",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            buildProcess.Start();
            var output = await buildProcess.StandardOutput.ReadToEndAsync();
            var error = await buildProcess.StandardError.ReadToEndAsync();
            await buildProcess.WaitForExitAsync(cancellationToken);

            var isHealthy = buildProcess.ExitCode == 0;
            var message = isHealthy ? "Module compiles successfully" : $"Compilation failed: {error}";

            return new HealthCheckResult
            {
                CheckName = "Compilation",
                Description = "Module compilation status",
                IsHealthy = isHealthy,
                Severity = isHealthy ? HealthCheckSeverity.Info : HealthCheckSeverity.Error,
                Message = message,
                Duration = stopwatch.Elapsed,
                Data = { ["ExitCode"] = buildProcess.ExitCode, ["Output"] = output, ["Error"] = error }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                CheckName = "Compilation",
                Description = "Module compilation status",
                IsHealthy = false,
                Severity = HealthCheckSeverity.Critical,
                Message = $"Compilation check failed: {ex.Message}",
                Exception = ex.ToString(),
                Duration = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Checks module dependencies
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    private async Task<HealthCheckResult> CheckModuleDependenciesAsync(string moduleName, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath))
            {
                return new HealthCheckResult
                {
                    CheckName = "Dependencies",
                    Description = "Module dependency validation",
                    IsHealthy = false,
                    Severity = HealthCheckSeverity.Error,
                    Message = "Project file not found for dependency check",
                    Duration = stopwatch.Elapsed
                };
            }

            // Run dependency validation script
            var validationScript = Path.Combine(Path.GetDirectoryName(projectPath)!, "..", "..", "tools", "ValidateDependencies.ps1");

            if (!File.Exists(validationScript))
            {
                return new HealthCheckResult
                {
                    CheckName = "Dependencies",
                    Description = "Module dependency validation",
                    IsHealthy = true,
                    Severity = HealthCheckSeverity.Warning,
                    Message = "Dependency validation script not found, skipping check",
                    Duration = stopwatch.Elapsed
                };
            }

            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = $"-ExecutionPolicy Bypass -File \"{validationScript}\" -ProjectPath \"{Path.GetDirectoryName(projectPath)}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync(cancellationToken);

            var isHealthy = process.ExitCode == 0;
            var message = isHealthy ? "All dependencies are valid" : $"Dependency issues detected: {error}";

            return new HealthCheckResult
            {
                CheckName = "Dependencies",
                Description = "Module dependency validation",
                IsHealthy = isHealthy,
                Severity = isHealthy ? HealthCheckSeverity.Info : HealthCheckSeverity.Warning,
                Message = message,
                Duration = stopwatch.Elapsed,
                Data = { ["Output"] = output, ["Error"] = error }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                CheckName = "Dependencies",
                Description = "Module dependency validation",
                IsHealthy = false,
                Severity = HealthCheckSeverity.Error,
                Message = $"Dependency check failed: {ex.Message}",
                Exception = ex.ToString(),
                Duration = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Checks architectural compliance
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    private async Task<HealthCheckResult> CheckArchitecturalComplianceAsync(string moduleName, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var projectPath = GetModuleProjectPath(moduleName);
            if (string.IsNullOrEmpty(projectPath))
            {
                return new HealthCheckResult
                {
                    CheckName = "ArchitecturalCompliance",
                    Description = "Architectural compliance validation",
                    IsHealthy = false,
                    Severity = HealthCheckSeverity.Error,
                    Message = "Project file not found for compliance check",
                    Duration = stopwatch.Elapsed
                };
            }

            // Run build with analyzers to check compliance
            var buildProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"build \"{projectPath}\" --no-restore --verbosity normal",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            buildProcess.Start();
            var output = await buildProcess.StandardOutput.ReadToEndAsync();
            var error = await buildProcess.StandardError.ReadToEndAsync();
            await buildProcess.WaitForExitAsync(cancellationToken);

            // Count ADF analyzer warnings/errors
            var adfWarnings = CountAnalyzerIssues(output, "ADF");
            var isHealthy = adfWarnings == 0;
            var severity = adfWarnings > 10 ? HealthCheckSeverity.Error :
                          adfWarnings > 5 ? HealthCheckSeverity.Warning : HealthCheckSeverity.Info;

            var message = isHealthy ? "Module is architecturally compliant" :
                         $"Found {adfWarnings} architectural compliance issues";

            return new HealthCheckResult
            {
                CheckName = "ArchitecturalCompliance",
                Description = "Architectural compliance validation",
                IsHealthy = isHealthy,
                Severity = severity,
                Message = message,
                Duration = stopwatch.Elapsed,
                Data = { ["ADFWarnings"] = adfWarnings, ["BuildOutput"] = output }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                CheckName = "ArchitecturalCompliance",
                Description = "Architectural compliance validation",
                IsHealthy = false,
                Severity = HealthCheckSeverity.Critical,
                Message = $"Compliance check failed: {ex.Message}",
                Exception = ex.ToString(),
                Duration = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Checks performance metrics
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    private async Task<HealthCheckResult> CheckPerformanceMetricsAsync(string moduleName, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Get current process metrics
            using var currentProcess = Process.GetCurrentProcess();
            var memoryUsage = currentProcess.WorkingSet64;
            var cpuTime = currentProcess.TotalProcessorTime;

            // Check if module has performance tests
            var projectPath = GetModuleProjectPath(moduleName);
            var hasPerformanceTests = HasPerformanceTests(projectPath);

            var isHealthy = memoryUsage < 500 * 1024 * 1024; // 500MB threshold
            var severity = isHealthy ? HealthCheckSeverity.Info : HealthCheckSeverity.Warning;

            var message = hasPerformanceTests ?
                         $"Performance metrics collected (Memory: {memoryUsage / 1024 / 1024}MB)" :
                         "No performance tests found for module";

            return new HealthCheckResult
            {
                CheckName = "Performance",
                Description = "Performance metrics validation",
                IsHealthy = isHealthy,
                Severity = severity,
                Message = message,
                Duration = stopwatch.Elapsed,
                Data = {
                    ["MemoryUsageMB"] = memoryUsage / 1024 / 1024,
                    ["CPUTimeMs"] = cpuTime.TotalMilliseconds,
                    ["HasPerformanceTests"] = hasPerformanceTests
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                CheckName = "Performance",
                Description = "Performance metrics validation",
                IsHealthy = false,
                Severity = HealthCheckSeverity.Error,
                Message = $"Performance check failed: {ex.Message}",
                Exception = ex.ToString(),
                Duration = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Checks service registration
    /// </summary>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    private async Task<HealthCheckResult> CheckServiceRegistrationAsync(string moduleName, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Check if module has ServiceCollectionExtensions
            var projectPath = GetModuleProjectPath(moduleName);
            var hasServiceExtensions = HasServiceCollectionExtensions(projectPath);

            var isHealthy = hasServiceExtensions;
            var severity = isHealthy ? HealthCheckSeverity.Info : HealthCheckSeverity.Warning;
            var message = isHealthy ? "Module has proper service registration" :
                         "Module missing ServiceCollectionExtensions";

            return new HealthCheckResult
            {
                CheckName = "ServiceRegistration",
                Description = "Service registration validation",
                IsHealthy = isHealthy,
                Severity = severity,
                Message = message,
                Duration = stopwatch.Elapsed,
                Data = { ["HasServiceExtensions"] = hasServiceExtensions }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                CheckName = "ServiceRegistration",
                Description = "Service registration validation",
                IsHealthy = false,
                Severity = HealthCheckSeverity.Error,
                Message = $"Service registration check failed: {ex.Message}",
                Exception = ex.ToString(),
                Duration = stopwatch.Elapsed
            };
        }
    }

    /// <summary>
    /// Disposes resources used by the monitor
    /// </summary>
    /// <param name="disposing">Whether disposing is in progress</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _healthCheckTimer?.Dispose();
            _alertProcessingTimer?.Dispose();
            _cleanupTimer?.Dispose();
        }

        base.Dispose(disposing);
    }
}
