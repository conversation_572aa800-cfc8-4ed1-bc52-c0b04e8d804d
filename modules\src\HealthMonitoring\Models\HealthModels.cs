// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Abstractions;

namespace ArtDesignFramework.HealthMonitoring.Core;

/// <summary>
/// Health monitoring configuration options
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class HealthMonitoringOptions
{
    /// <summary>
    /// Gets or sets the interval between health checks
    /// </summary>
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Gets or sets the timeout for individual health checks
    /// </summary>
    public TimeSpan CheckTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Gets or sets whether predictive monitoring is enabled
    /// </summary>
    public bool EnablePredictiveMonitoring { get; set; } = true;

    /// <summary>
    /// Gets or sets whether auto-remediation is enabled
    /// </summary>
    public bool EnableAutoRemediation { get; set; } = false;

    /// <summary>
    /// Gets or sets the maximum number of health records to retain
    /// </summary>
    public int MaxHealthRecords { get; set; } = 1000;

    /// <summary>
    /// Gets or sets the health record retention period
    /// </summary>
    public TimeSpan HealthRecordRetention { get; set; } = TimeSpan.FromDays(7);

    /// <summary>
    /// Gets or sets whether detailed logging is enabled
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Gets or sets the list of modules to monitor
    /// </summary>
    public List<string> ModulesToMonitor { get; set; } = new();

    /// <summary>
    /// Gets or sets the alert thresholds
    /// </summary>
    public Dictionary<string, double> AlertThresholds { get; set; } = new();
}

/// <summary>
/// Module health information
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class ModuleHealth
{
    /// <summary>
    /// Gets or sets the module name
    /// </summary>
    public string ModuleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the module version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the health status
    /// </summary>
    public ModuleHealthStatus Status { get; set; } = ModuleHealthStatus.Unknown;

    /// <summary>
    /// Gets or sets the health score (0.0 to 1.0)
    /// </summary>
    public double HealthScore { get; set; } = 0.0;

    /// <summary>
    /// Gets or sets when the health check was last performed
    /// </summary>
    public DateTime LastChecked { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the duration of the health check
    /// </summary>
    public TimeSpan CheckDuration { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// Gets the list of health check results
    /// </summary>
    public List<HealthCheckResult> CheckResults { get; } = new();

    /// <summary>
    /// Gets the list of errors
    /// </summary>
    public List<string> Errors { get; } = new();

    /// <summary>
    /// Gets the list of warnings
    /// </summary>
    public List<string> Warnings { get; } = new();

    /// <summary>
    /// Gets the metrics dictionary
    /// </summary>
    public Dictionary<string, object> Metrics { get; } = new();
}

/// <summary>
/// Module health status enumeration
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public enum ModuleHealthStatus
{
    /// <summary>
    /// Health status is unknown
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// Module is healthy
    /// </summary>
    Healthy = 1,

    /// <summary>
    /// Module is degraded but functional
    /// </summary>
    Degraded = 2,

    /// <summary>
    /// Module is unhealthy
    /// </summary>
    Unhealthy = 3,

    /// <summary>
    /// Module is in critical state
    /// </summary>
    Critical = 4
}

/// <summary>
/// Health check result
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class HealthCheckResult
{
    /// <summary>
    /// Gets or sets the check name
    /// </summary>
    public string CheckName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the check description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the check is healthy
    /// </summary>
    public bool IsHealthy { get; set; } = false;

    /// <summary>
    /// Gets or sets the severity level
    /// </summary>
    public HealthCheckSeverity Severity { get; set; } = HealthCheckSeverity.Info;

    /// <summary>
    /// Gets or sets the result message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the exception details if any
    /// </summary>
    public string? Exception { get; set; }

    /// <summary>
    /// Gets or sets the check duration
    /// </summary>
    public TimeSpan Duration { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// Gets the additional data dictionary
    /// </summary>
    public Dictionary<string, object> Data { get; } = new();
}

/// <summary>
/// Health check severity enumeration
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public enum HealthCheckSeverity
{
    /// <summary>
    /// Informational message
    /// </summary>
    Info = 0,

    /// <summary>
    /// Warning message
    /// </summary>
    Warning = 1,

    /// <summary>
    /// Error message
    /// </summary>
    Error = 2,

    /// <summary>
    /// Critical message
    /// </summary>
    Critical = 3
}

/// <summary>
/// Health alert information
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class HealthAlert
{
    /// <summary>
    /// Gets or sets the alert ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Gets or sets the module name
    /// </summary>
    public string ModuleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the alert severity
    /// </summary>
    public HealthCheckSeverity Severity { get; set; } = HealthCheckSeverity.Info;

    /// <summary>
    /// Gets or sets the alert message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the alert description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the metric that triggered the alert
    /// </summary>
    public string TriggerMetric { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current value
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Gets or sets the threshold value
    /// </summary>
    public object? ThresholdValue { get; set; }

    /// <summary>
    /// Gets or sets the alert timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets whether the alert is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets when the alert was acknowledged
    /// </summary>
    public DateTime? AcknowledgedAt { get; set; }

    /// <summary>
    /// Gets or sets who acknowledged the alert
    /// </summary>
    public string? AcknowledgedBy { get; set; }
}

/// <summary>
/// Health trend analysis
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class HealthTrend
{
    /// <summary>
    /// Gets or sets the module name
    /// </summary>
    public string ModuleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the metric name
    /// </summary>
    public string MetricName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the trend direction
    /// </summary>
    public TrendDirection Direction { get; set; } = TrendDirection.Stable;

    /// <summary>
    /// Gets or sets the trend strength (0.0 to 1.0)
    /// </summary>
    public double Strength { get; set; } = 0.0;

    /// <summary>
    /// Gets or sets the confidence level (0.0 to 1.0)
    /// </summary>
    public double Confidence { get; set; } = 0.0;

    /// <summary>
    /// Gets or sets the predicted value
    /// </summary>
    public double PredictedValue { get; set; } = 0.0;

    /// <summary>
    /// Gets or sets the prediction timeframe
    /// </summary>
    public TimeSpan PredictionTimeframe { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// Gets the data points for the trend
    /// </summary>
    public List<(DateTime Timestamp, double Value)> DataPoints { get; set; } = new();

    /// <summary>
    /// Gets the recommendations based on the trend
    /// </summary>
    public List<string> Recommendations { get; } = new();
}

/// <summary>
/// Trend direction enumeration
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public enum TrendDirection
{
    /// <summary>
    /// Trend is stable
    /// </summary>
    Stable = 0,

    /// <summary>
    /// Trend is improving
    /// </summary>
    Improving = 1,

    /// <summary>
    /// Trend is degrading
    /// </summary>
    Degrading = 2
}
