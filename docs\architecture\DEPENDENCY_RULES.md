# ArtDesignFramework Dependency Rules

Comprehensive rules and guidelines for module dependencies to maintain architectural integrity and prevent circular dependencies.

**Last Updated: 2025-01-27 22:50:00 UTC**

## Overview

This document defines the strict dependency rules that govern how modules within the ArtDesignFramework can interact with each other. These rules are enforced through automated build-time validation and must be followed by all developers.

## Dependency Hierarchy

### Layer 1: Foundation Layer
```
ArtDesignFramework.Abstractions
├── No dependencies
└── Contains: Interfaces, Attributes, Shared Contracts
```

**Allowed Dependencies**: None
**Purpose**: Provides shared abstractions for all framework modules
**Consumers**: All other framework modules

### Layer 2: Core Framework Layer
```
ArtDesignFramework.Core
├── Depends on: Abstractions
└── Contains: Core Services, Framework Implementation
```

**Allowed Dependencies**: 
- ✅ ArtDesignFramework.Abstractions

**Purpose**: Core framework functionality and service implementations
**Consumers**: All application modules and test frameworks

### Layer 3: Infrastructure Layer
```
ArtDesignFramework.TestFramework
├── Depends on: Abstractions, Core
└── Contains: Testing Infrastructure, Mocks, Utilities
```

**Allowed Dependencies**:
- ✅ ArtDesignFramework.Abstractions
- ✅ ArtDesignFramework.Core

**Purpose**: Testing infrastructure and utilities
**Consumers**: All test projects and modules requiring testing support

### Layer 4: Application Modules Layer
```
Application Modules (UserInterface, Performance, DataAccess, etc.)
├── Depends on: Abstractions, Core, TestFramework (for tests only)
└── Contains: Specific functionality implementations
```

**Allowed Dependencies**:
- ✅ ArtDesignFramework.Abstractions
- ✅ ArtDesignFramework.Core
- ✅ ArtDesignFramework.TestFramework (test projects only)
- ✅ Other application modules (with restrictions)

**Purpose**: Specific functionality implementations
**Consumers**: Applications and other modules (following rules)

### Layer 5: Application Layer
```
Applications (ClockDesktopApp, etc.)
├── Depends on: All framework layers and required modules
└── Contains: End-user applications
```

**Allowed Dependencies**:
- ✅ All framework modules
- ✅ External packages as needed

**Purpose**: End-user applications
**Consumers**: End users

## Dependency Rules Matrix

| From Module | To Abstractions | To Core | To TestFramework | To App Modules | To Applications |
|-------------|----------------|---------|------------------|----------------|-----------------|
| **Abstractions** | ❌ | ❌ | ❌ | ❌ | ❌ |
| **Core** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **TestFramework** | ✅ | ✅ | ❌ | ❌ | ❌ |
| **App Modules** | ✅ | ✅ | ✅* | ⚠️** | ❌ |
| **Applications** | ✅ | ✅ | ✅ | ✅ | ❌ |

**Legend:**
- ✅ Allowed
- ❌ Forbidden
- ✅* Test projects only
- ⚠️** With restrictions (see Inter-Module Dependencies)

## Inter-Module Dependencies

### Allowed Inter-Module Dependencies
Application modules may depend on other application modules following these rules:

#### 1. Functional Dependencies
```
UserInterface → Performance (for performance monitoring)
UserInterface → DataAccess (for data binding)
DataAccess → Performance (for query optimization)
```

#### 2. Plugin Dependencies
```
PluginSystem → Core (for plugin infrastructure)
EffectsEngine → PluginSystem (for effect plugins)
VectorGraphics → EffectsEngine (for vector effects)
```

#### 3. AI Module Dependencies
```
AILighting → AIModelManager (for AI models)
AIModelManager → Performance (for model optimization)
```

### Forbidden Inter-Module Dependencies

#### 1. Circular Dependencies
```
❌ UserInterface → Performance → UserInterface
❌ DataAccess → PluginSystem → DataAccess
❌ Any circular reference chain
```

#### 2. Reverse Dependencies
```
❌ Core → UserInterface
❌ Core → Any application module
❌ TestFramework → Application modules (except for testing)
```

#### 3. Cross-Domain Dependencies
```
❌ AILighting → VectorGraphics (different domains)
❌ FreeFonts → EffectsEngine (unrelated functionality)
❌ ClockDesktopApp → AIModelManager (application to infrastructure)
```

## Validation Rules

### Build-Time Validation
1. **Project Reference Validation**: All project references must exist and be valid
2. **Circular Dependency Detection**: No circular dependencies allowed at any level
3. **Layer Violation Detection**: Dependencies must follow the layer hierarchy
4. **Forbidden Reference Detection**: Explicitly forbidden references are blocked

### Runtime Validation
1. **Service Registration Validation**: All services must be properly registered
2. **Interface Implementation Validation**: All interfaces must have valid implementations
3. **Dependency Injection Validation**: All dependencies must be resolvable

## Exception Handling

### Temporary Exceptions
In rare cases, temporary exceptions to dependency rules may be granted:

1. **Migration Scenarios**: During module refactoring
2. **Legacy Support**: For backward compatibility
3. **Performance Optimization**: For critical performance paths

### Exception Process
1. Create architectural decision record (ADR)
2. Document rationale and timeline
3. Implement monitoring for the exception
4. Plan for exception removal
5. Get architectural approval

## Best Practices

### 1. Dependency Injection
```csharp
// ✅ Good: Use dependency injection
public class UserService
{
    private readonly IDataRepository _repository;
    
    public UserService(IDataRepository repository)
    {
        _repository = repository;
    }
}

// ❌ Bad: Direct instantiation
public class UserService
{
    private readonly DataRepository _repository = new DataRepository();
}
```

### 2. Interface Segregation
```csharp
// ✅ Good: Specific interfaces
public interface IUserReader
{
    Task<User> GetUserAsync(int id);
}

public interface IUserWriter
{
    Task SaveUserAsync(User user);
}

// ❌ Bad: Monolithic interface
public interface IUserRepository
{
    // 20+ methods for all user operations
}
```

### 3. Abstraction Usage
```csharp
// ✅ Good: Depend on abstractions
public class UserController
{
    private readonly IUserService _userService;
}

// ❌ Bad: Depend on concrete types
public class UserController
{
    private readonly UserService _userService;
}
```

## Monitoring and Enforcement

### Automated Checks
1. **MSBuild Targets**: Validate dependencies during build
2. **Roslyn Analyzers**: Real-time dependency validation
3. **Integration Tests**: Runtime dependency validation
4. **CI/CD Pipeline**: Continuous dependency monitoring

### Manual Reviews
1. **Architecture Reviews**: For new modules and major changes
2. **Dependency Audits**: Regular review of all dependencies
3. **Performance Reviews**: Impact assessment of dependency changes
4. **Security Reviews**: Security implications of dependencies

## Troubleshooting

### Common Issues

#### 1. Circular Dependency Detected
```
Error: Circular dependency detected: ModuleA → ModuleB → ModuleA
Solution: Extract shared functionality to Abstractions or create new shared module
```

#### 2. Layer Violation
```
Error: Core module cannot depend on UserInterface module
Solution: Move shared functionality to Abstractions or use dependency injection
```

#### 3. Missing Dependency
```
Error: Project reference 'ModuleX' not found
Solution: Add proper project reference or remove usage
```

### Resolution Steps
1. Identify the dependency violation
2. Analyze the architectural impact
3. Choose appropriate resolution strategy
4. Implement changes following rules
5. Validate with automated checks
6. Update documentation

## Tools and Utilities

### Dependency Analysis Tools
- **DependencyValidator.ps1**: PowerShell script for dependency analysis
- **CircularDependencyDetector**: MSBuild target for cycle detection
- **ArchitecturalComplianceAnalyzer**: Roslyn analyzer for compliance

### Visualization Tools
- **DependencyGraph.ps1**: Generate dependency graphs
- **ModuleMap.html**: Interactive module dependency visualization
- **ComplianceReport.html**: Compliance status dashboard

## Change Management

### Adding New Dependencies
1. Verify dependency follows rules
2. Update this documentation
3. Add validation rules if needed
4. Test with automated checks
5. Update monitoring systems

### Removing Dependencies
1. Identify all consumers
2. Plan migration strategy
3. Update dependent modules
4. Remove dependency references
5. Validate with tests

### Modifying Rules
1. Create ADR for rule changes
2. Assess impact on existing modules
3. Plan migration timeline
4. Update validation tools
5. Communicate changes to team

---

**Note**: These dependency rules are enforced automatically during build and must be followed by all developers. Violations will cause build failures and must be resolved before code can be merged.
