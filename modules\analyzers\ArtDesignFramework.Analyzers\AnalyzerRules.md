# ArtDesignFramework Architectural Compliance Analyzer Rules

Last Updated: 2025-06-12 09:15:00 UTC

This document describes the architectural compliance rules enforced by the ArtDesignFramework analyzers.

## Rule Categories

### ADF0xxx - General Architectural Compliance

#### ADF0001 - Missing [TestableMethod] Attribute
- **Severity**: Warning
- **Description**: Public methods should have [TestableMethod] attribute for framework compliance
- **Fix**: Add [TestableMethod] attribute to public methods

#### ADF0002 - Missing XML Documentation with Timestamp
- **Severity**: Warning  
- **Description**: Classes and methods should have XML documentation with required timestamp format
- **Fix**: Add XML documentation with "Last Updated: YYYY-MM-DD HH:mm:ss UTC" timestamp

#### ADF0003 - Invalid ViewModel Pattern
- **Severity**: Error
- **Description**: ViewModels should implement INotifyPropertyChanged and follow MVVM naming conventions
- **Fix**: Implement INotifyPropertyChanged interface in ViewModel classes

#### ADF0004 - Invalid Service Registration Pattern
- **Severity**: Warning
- **Description**: Service registration should use established framework patterns with proper lifetime management
- **Fix**: Use proper service registration patterns in ServiceCollectionExtensions

#### ADF0005 - Invalid Namespace Convention
- **Severity**: Info
- **Description**: Namespaces should follow ArtDesignFramework.ModuleName.SubNamespace convention
- **Fix**: Update namespace to follow framework conventions

### ADF1xxx - Service Registration Compliance

#### ADF1001 - Missing ServiceCollectionExtensions Class
- **Severity**: Warning
- **Description**: Modules should have a ServiceCollectionExtensions class for proper service registration
- **Fix**: Create ServiceCollectionExtensions class with Add{ModuleName} method

#### ADF1002 - Invalid Service Lifetime
- **Severity**: Warning
- **Description**: Service registration uses inappropriate lifetime (Singleton/Scoped/Transient)
- **Fix**: Use appropriate service lifetime based on usage patterns

#### ADF1003 - Missing Interface Registration
- **Severity**: Info
- **Description**: Services should be registered by their interfaces for better testability
- **Fix**: Register services by their interfaces instead of concrete types

#### ADF1004 - Invalid Extension Method Name
- **Severity**: Info
- **Description**: Service extension methods should follow 'Add{ModuleName}' naming convention
- **Fix**: Rename extension method to follow Add{ModuleName} pattern

### ADF2xxx - MVVM Pattern Compliance

#### ADF2001 - ViewModel Must Implement INotifyPropertyChanged
- **Severity**: Error
- **Description**: ViewModels must implement INotifyPropertyChanged interface
- **Fix**: Implement INotifyPropertyChanged in ViewModel classes

#### ADF2002 - Property Should Raise PropertyChanged Event
- **Severity**: Warning
- **Description**: ViewModel properties should raise PropertyChanged event in setter
- **Fix**: Add PropertyChanged event raising in property setters

#### ADF2003 - Command Should Implement ICommand
- **Severity**: Warning
- **Description**: Command properties should implement ICommand interface
- **Fix**: Use ICommand, RelayCommand, or DelegateCommand for command properties

#### ADF2004 - ViewModel Should Not Reference UI
- **Severity**: Error
- **Description**: ViewModels should not directly reference UI elements
- **Fix**: Remove direct UI element references from ViewModels

#### ADF2005 - ViewModel Should Be Testable
- **Severity**: Info
- **Description**: ViewModels should have [TestableClass] attribute and testable constructor
- **Fix**: Add [TestableClass] attribute and ensure testable design

## Configuration

### Severity Levels
The analyzers can be configured in Directory.Build.props:

```xml
<!-- Configure analyzer severity levels -->
<WarningsAsErrors>ADF0003;ADF2001;ADF2004</WarningsAsErrors>
<WarningsNotAsErrors>ADF0001;ADF0002;ADF1001;ADF2002;ADF2005</WarningsNotAsErrors>
```

### Disabling Rules
To disable specific rules for a project:

```xml
<PropertyGroup>
  <NoWarn>$(NoWarn);ADF0001;ADF0002</NoWarn>
</PropertyGroup>
```

### Per-File Suppression
To suppress rules for specific files:

```csharp
#pragma warning disable ADF0001 // Missing TestableMethod attribute
public void LegacyMethod() { }
#pragma warning restore ADF0001
```

## Best Practices

1. **Always add [TestableMethod] attributes** to public methods for better testing support
2. **Include XML documentation with timestamps** for all public APIs
3. **Follow MVVM patterns** strictly in ViewModel classes
4. **Use proper service registration patterns** with appropriate lifetimes
5. **Register services by interfaces** for better testability and flexibility
6. **Follow framework naming conventions** for consistency

## Integration

The analyzers are automatically included in all framework projects via Directory.Build.props and provide real-time feedback during development and build processes.
