using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;
using System;
using System.Collections.Immutable;
using System.Linq;

namespace ArtDesignFramework.Analyzers
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> analyzer for ArtDesignFramework architectural compliance checking.
    /// Last Updated: 2025-06-12 08:50:00 UTC
    /// Validates MVVM patterns, [Testable] attribute usage, service registration, and other architectural standards.
    /// </summary>
    [DiagnosticAnalyzer(LanguageNames.CSharp)]
    public class ArchitecturalComplianceAnalyzer : DiagnosticAnalyzer
    {
        // Diagnostic descriptors for different architectural violations
        public static readonly DiagnosticDescriptor MissingTestableAttributeRule = new DiagnosticDescriptor(
            "ADF0001",
            "Method should have [TestableMethod] attribute",
            "Public method '{0}' in class '{1}' should have [TestableMethod] attribute for framework compliance",
            "Architecture",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "All public methods in framework classes should have [TestableMethod] attribute for testing and documentation purposes.");

        public static readonly DiagnosticDescriptor MissingXmlDocumentationRule = new DiagnosticDescriptor(
            "ADF0002",
            "Missing XML documentation with timestamp",
            "Class or method '{0}' is missing XML documentation with required timestamp format",
            "Documentation",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "All public classes and methods should have XML documentation with 'Last Updated: YYYY-MM-DD HH:mm:ss UTC' timestamp.");

        public static readonly DiagnosticDescriptor InvalidViewModelPatternRule = new DiagnosticDescriptor(
            "ADF0003",
            "ViewModel does not follow MVVM pattern",
            "ViewModel '{0}' should implement INotifyPropertyChanged and follow MVVM naming conventions",
            "Architecture",
            DiagnosticSeverity.Error,
            isEnabledByDefault: true,
            description: "ViewModels must implement INotifyPropertyChanged interface and follow proper MVVM patterns.");

        public static readonly DiagnosticDescriptor InvalidServiceRegistrationRule = new DiagnosticDescriptor(
            "ADF0004",
            "Service registration does not follow framework patterns",
            "Service registration in '{0}' should use established framework patterns with proper lifetime management",
            "Architecture",
            DiagnosticSeverity.Warning,
            isEnabledByDefault: true,
            description: "Service registrations should follow framework patterns using ServiceCollectionExtensions.");

        public static readonly DiagnosticDescriptor InvalidNamespaceConventionRule = new DiagnosticDescriptor(
            "ADF0005",
            "Namespace does not follow framework conventions",
            "Namespace '{0}' should follow ArtDesignFramework.ModuleName.SubNamespace convention",
            "Naming",
            DiagnosticSeverity.Info,
            isEnabledByDefault: true,
            description: "All namespaces should follow the established framework naming conventions.");

        public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics =>
            ImmutableArray.Create(
                MissingTestableAttributeRule,
                MissingXmlDocumentationRule,
                InvalidViewModelPatternRule,
                InvalidServiceRegistrationRule,
                InvalidNamespaceConventionRule);

        public override void Initialize(AnalysisContext context)
        {
            context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
            context.EnableConcurrentExecution();
            context.RegisterSyntaxNodeAction(AnalyzeClassDeclaration, SyntaxKind.ClassDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzeMethodDeclaration, SyntaxKind.MethodDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzeNamespaceDeclaration, SyntaxKind.NamespaceDeclaration);
            context.RegisterSyntaxNodeAction(AnalyzeFileScopedNamespaceDeclaration, SyntaxKind.FileScopedNamespaceDeclaration);
        }

        private static void AnalyzeClassDeclaration(SyntaxNodeAnalysisContext context)
        {
            var classDeclaration = (ClassDeclarationSyntax)context.Node;
            var className = classDeclaration.Identifier.ValueText;

            // Check ViewModel pattern compliance
            if (className.EndsWith("ViewModel"))
            {
                CheckViewModelCompliance(context, classDeclaration, className);
            }

            // Check XML documentation
            CheckXmlDocumentation(context, classDeclaration, className);

            // Check service registration patterns
            if (className.EndsWith("Extensions") && className.Contains("ServiceCollection"))
            {
                CheckServiceRegistrationPatterns(context, classDeclaration, className);
            }
        }

        private static void AnalyzeMethodDeclaration(SyntaxNodeAnalysisContext context)
        {
            var methodDeclaration = (MethodDeclarationSyntax)context.Node;
            var methodName = methodDeclaration.Identifier.ValueText;

            // Skip private methods, constructors, and property accessors
            if (!methodDeclaration.Modifiers.Any(SyntaxKind.PublicKeyword) ||
                methodName.StartsWith("get_") || methodName.StartsWith("set_"))
            {
                return;
            }

            var className = GetContainingClassName(methodDeclaration);
            
            // Check for [TestableMethod] attribute
            CheckTestableMethodAttribute(context, methodDeclaration, methodName, className);

            // Check XML documentation
            CheckXmlDocumentation(context, methodDeclaration, methodName);
        }

        private static void AnalyzeNamespaceDeclaration(SyntaxNodeAnalysisContext context)
        {
            var namespaceDeclaration = (NamespaceDeclarationSyntax)context.Node;
            CheckNamespaceConvention(context, namespaceDeclaration.Name.ToString(), namespaceDeclaration);
        }

        private static void AnalyzeFileScopedNamespaceDeclaration(SyntaxNodeAnalysisContext context)
        {
            var namespaceDeclaration = (FileScopedNamespaceDeclarationSyntax)context.Node;
            CheckNamespaceConvention(context, namespaceDeclaration.Name.ToString(), namespaceDeclaration);
        }

        private static void CheckViewModelCompliance(SyntaxNodeAnalysisContext context, ClassDeclarationSyntax classDeclaration, string className)
        {
            var semanticModel = context.SemanticModel;
            var classSymbol = semanticModel.GetDeclaredSymbol(classDeclaration);

            if (classSymbol != null)
            {
                // Check if implements INotifyPropertyChanged
                var implementsINotifyPropertyChanged = classSymbol.AllInterfaces
                    .Any(i => i.Name == "INotifyPropertyChanged");

                if (!implementsINotifyPropertyChanged)
                {
                    var diagnostic = Diagnostic.Create(
                        InvalidViewModelPatternRule,
                        classDeclaration.Identifier.GetLocation(),
                        className);
                    context.ReportDiagnostic(diagnostic);
                }
            }
        }

        private static void CheckTestableMethodAttribute(SyntaxNodeAnalysisContext context, MethodDeclarationSyntax methodDeclaration, string methodName, string className)
        {
            var hasTestableAttribute = methodDeclaration.AttributeLists
                .SelectMany(al => al.Attributes)
                .Any(attr => attr.Name.ToString().Contains("TestableMethod") || attr.Name.ToString().Contains("Testable"));

            if (!hasTestableAttribute)
            {
                var diagnostic = Diagnostic.Create(
                    MissingTestableAttributeRule,
                    methodDeclaration.Identifier.GetLocation(),
                    methodName,
                    className);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckXmlDocumentation(SyntaxNodeAnalysisContext context, SyntaxNode node, string memberName)
        {
            var documentationComment = node.GetLeadingTrivia()
                .FirstOrDefault(t => t.IsKind(SyntaxKind.SingleLineDocumentationCommentTrivia) ||
                                   t.IsKind(SyntaxKind.MultiLineDocumentationCommentTrivia));

            if (documentationComment.IsKind(SyntaxKind.None))
            {
                var diagnostic = Diagnostic.Create(
                    MissingXmlDocumentationRule,
                    node.GetLocation(),
                    memberName);
                context.ReportDiagnostic(diagnostic);
                return;
            }

            // Check for timestamp format
            var documentationText = documentationComment.ToString();
            if (!documentationText.Contains("Last Updated:") || 
                !System.Text.RegularExpressions.Regex.IsMatch(documentationText, @"Last Updated: \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC"))
            {
                var diagnostic = Diagnostic.Create(
                    MissingXmlDocumentationRule,
                    node.GetLocation(),
                    memberName);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static void CheckServiceRegistrationPatterns(SyntaxNodeAnalysisContext context, ClassDeclarationSyntax classDeclaration, string className)
        {
            // Check if methods follow proper service registration patterns
            var methods = classDeclaration.Members.OfType<MethodDeclarationSyntax>();
            
            foreach (var method in methods)
            {
                if (method.Modifiers.Any(SyntaxKind.PublicKeyword) && 
                    method.Modifiers.Any(SyntaxKind.StaticKeyword) &&
                    method.ReturnType.ToString().Contains("IServiceCollection"))
                {
                    // This is likely a service registration method
                    // Check for proper patterns (this is a simplified check)
                    var methodBody = method.Body?.ToString() ?? "";
                    if (!methodBody.Contains("services.Add") && !methodBody.Contains("services.Configure"))
                    {
                        var diagnostic = Diagnostic.Create(
                            InvalidServiceRegistrationRule,
                            method.Identifier.GetLocation(),
                            method.Identifier.ValueText);
                        context.ReportDiagnostic(diagnostic);
                    }
                }
            }
        }

        private static void CheckNamespaceConvention(SyntaxNodeAnalysisContext context, string namespaceName, SyntaxNode node)
        {
            if (!namespaceName.StartsWith("ArtDesignFramework"))
            {
                var diagnostic = Diagnostic.Create(
                    InvalidNamespaceConventionRule,
                    node.GetLocation(),
                    namespaceName);
                context.ReportDiagnostic(diagnostic);
            }
        }

        private static string GetContainingClassName(MethodDeclarationSyntax methodDeclaration)
        {
            var classDeclaration = methodDeclaration.Ancestors().OfType<ClassDeclarationSyntax>().FirstOrDefault();
            return classDeclaration?.Identifier.ValueText ?? "Unknown";
        }
    }
}
