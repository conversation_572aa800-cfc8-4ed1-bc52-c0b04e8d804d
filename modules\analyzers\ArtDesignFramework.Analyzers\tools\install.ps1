# ArtDesignFramework Analyzers Installation Script
# Last Updated: 2025-06-12 09:05:00 UTC
# Installs architectural compliance analyzers for the framework

param($installPath, $toolsPath, $package, $project)

Write-Host "Installing ArtDesignFramework.Analyzers..." -ForegroundColor Green

try {
    # Add analyzer reference to the project
    $analyzerPath = Join-Path $toolsPath "..\analyzers\dotnet\cs\ArtDesignFramework.Analyzers.dll"
    
    if (Test-Path $analyzerPath) {
        Write-Host "Adding analyzer reference: $analyzerPath" -ForegroundColor Yellow
        
        # Add the analyzer to the project
        $project.Object.References.Add($analyzerPath)
        
        Write-Host "ArtDesignFramework.Analyzers installed successfully!" -ForegroundColor Green
        Write-Host "The following architectural compliance checks are now active:" -ForegroundColor Cyan
        Write-Host "  - [TestableMethod] attribute validation" -ForegroundColor White
        Write-Host "  - XML documentation with timestamp requirements" -ForegroundColor White
        Write-Host "  - MVVM pattern compliance" -ForegroundColor White
        Write-Host "  - Service registration patterns" -ForegroundColor White
        Write-Host "  - Namespace conventions" -ForegroundColor White
    }
    else {
        Write-Warning "Analyzer assembly not found at: $analyzerPath"
    }
}
catch {
    Write-Error "Failed to install ArtDesignFramework.Analyzers: $_"
}
