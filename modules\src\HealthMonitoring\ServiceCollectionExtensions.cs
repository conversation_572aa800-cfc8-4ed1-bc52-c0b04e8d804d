// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ArtDesignFramework.HealthMonitoring.Core;
using ArtDesignFramework.HealthMonitoring.Services;
using ArtDesignFramework.Abstractions;

namespace ArtDesignFramework.HealthMonitoring;

/// <summary>
/// Service collection extensions for health monitoring module
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds automated integrity monitoring services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration instance</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddAutomatedIntegrityMonitoring", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public static IServiceCollection AddAutomatedIntegrityMonitoring(
        this IServiceCollection services, 
        IConfiguration? configuration = null)
    {
        ArgumentNullException.ThrowIfNull(services);

        // Configure health monitoring options
        if (configuration != null)
        {
            services.Configure<HealthMonitoringOptions>(
                configuration.GetSection("HealthMonitoring"));
        }
        else
        {
            services.Configure<HealthMonitoringOptions>(options =>
            {
                options.CheckInterval = TimeSpan.FromMinutes(5);
                options.CheckTimeout = TimeSpan.FromSeconds(30);
                options.EnablePredictiveMonitoring = true;
                options.EnableAutoRemediation = false;
                options.MaxHealthRecords = 1000;
                options.HealthRecordRetention = TimeSpan.FromDays(7);
                options.EnableDetailedLogging = false;
                
                // Configure default modules to monitor
                options.ModulesToMonitor.AddRange(new[]
                {
                    "Core",
                    "Abstractions", 
                    "TestFramework",
                    "UserInterface",
                    "Performance",
                    "DataAccess",
                    "EffectsEngine",
                    "PluginSystem",
                    "VectorGraphics",
                    "FontRendering",
                    "ImageHandling",
                    "WebServer",
                    "AILighting",
                    "AIModelManager",
                    "HealthMonitoring"
                });

                // Configure alert thresholds
                options.AlertThresholds["HealthScore"] = 0.8; // 80%
                options.AlertThresholds["ResponseTime"] = 5000; // 5 seconds
                options.AlertThresholds["ErrorRate"] = 0.05; // 5%
                options.AlertThresholds["MemoryUsage"] = 0.8; // 80%
            });
        }

        // Register core services
        services.AddSingleton<IModuleHealthMonitor, AutomatedIntegrityMonitor>();
        services.AddHostedService<AutomatedIntegrityMonitor>(provider => 
            (AutomatedIntegrityMonitor)provider.GetRequiredService<IModuleHealthMonitor>());

        // Add health checks integration
        services.AddHealthChecks()
            .AddCheck<FrameworkIntegrityHealthCheck>("framework-integrity")
            .AddCheck<ModuleCompilationHealthCheck>("module-compilation")
            .AddCheck<DependencyValidationHealthCheck>("dependency-validation")
            .AddCheck<ArchitecturalComplianceHealthCheck>("architectural-compliance");

        // Add logging if not already configured
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
        });

        return services;
    }

    /// <summary>
    /// Adds health monitoring with custom configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Configuration action</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddAutomatedIntegrityMonitoring", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public static IServiceCollection AddAutomatedIntegrityMonitoring(
        this IServiceCollection services,
        Action<HealthMonitoringOptions> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        services.Configure(configureOptions);
        return services.AddAutomatedIntegrityMonitoring();
    }

    /// <summary>
    /// Adds health monitoring for specific modules
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="modulesToMonitor">Specific modules to monitor</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddAutomatedIntegrityMonitoringForModules", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public static IServiceCollection AddAutomatedIntegrityMonitoringForModules(
        this IServiceCollection services,
        params string[] modulesToMonitor)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(modulesToMonitor);

        return services.AddAutomatedIntegrityMonitoring(options =>
        {
            options.ModulesToMonitor.Clear();
            options.ModulesToMonitor.AddRange(modulesToMonitor);
        });
    }

    /// <summary>
    /// Adds enterprise-grade health monitoring with enhanced features
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration instance</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddEnterpriseHealthMonitoring", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public static IServiceCollection AddEnterpriseHealthMonitoring(
        this IServiceCollection services,
        IConfiguration? configuration = null)
    {
        ArgumentNullException.ThrowIfNull(services);

        // Add base monitoring
        services.AddAutomatedIntegrityMonitoring(configuration);

        // Configure for enterprise features
        services.Configure<HealthMonitoringOptions>(options =>
        {
            options.CheckInterval = TimeSpan.FromMinutes(1); // More frequent checks
            options.EnablePredictiveMonitoring = true;
            options.EnableAutoRemediation = true; // Enable auto-remediation
            options.EnableDetailedLogging = true;
            options.MaxHealthRecords = 5000; // More history
            options.HealthRecordRetention = TimeSpan.FromDays(30); // Longer retention

            // Stricter thresholds for enterprise
            options.AlertThresholds["HealthScore"] = 0.9; // 90%
            options.AlertThresholds["ResponseTime"] = 3000; // 3 seconds
            options.AlertThresholds["ErrorRate"] = 0.02; // 2%
            options.AlertThresholds["MemoryUsage"] = 0.7; // 70%
        });

        // Add additional enterprise services
        services.AddSingleton<IHealthDashboardService, HealthDashboardService>();
        services.AddSingleton<IHealthReportingService, HealthReportingService>();
        services.AddSingleton<IHealthAlertingService, HealthAlertingService>();

        return services;
    }

    /// <summary>
    /// Adds health monitoring with real-time dashboard
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration instance</param>
    /// <returns>Service collection for chaining</returns>
    [TestableMethod("AddHealthMonitoringWithDashboard", IncludeParameterValidation = true, IncludeExceptionTests = true)]
    public static IServiceCollection AddHealthMonitoringWithDashboard(
        this IServiceCollection services,
        IConfiguration? configuration = null)
    {
        ArgumentNullException.ThrowIfNull(services);

        // Add base monitoring
        services.AddAutomatedIntegrityMonitoring(configuration);

        // Add dashboard services
        services.AddSingleton<IHealthDashboardService, HealthDashboardService>();
        services.AddSignalR(); // For real-time updates

        return services;
    }
}

/// <summary>
/// Framework integrity health check
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class FrameworkIntegrityHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IModuleHealthMonitor _healthMonitor;
    private readonly ILogger<FrameworkIntegrityHealthCheck> _logger;

    /// <summary>
    /// Initializes a new instance of the FrameworkIntegrityHealthCheck class
    /// </summary>
    /// <param name="healthMonitor">Health monitor instance</param>
    /// <param name="logger">Logger instance</param>
    public FrameworkIntegrityHealthCheck(
        IModuleHealthMonitor healthMonitor,
        ILogger<FrameworkIntegrityHealthCheck> logger)
    {
        _healthMonitor = healthMonitor ?? throw new ArgumentNullException(nameof(healthMonitor));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Performs framework integrity health check
    /// </summary>
    /// <param name="context">Health check context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    [TestableMethod("CheckHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 10000)]
    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var systemSummary = await _healthMonitor.GetSystemHealthSummaryAsync();
            var overallScore = (double)systemSummary["OverallHealthScore"];
            var meetsTarget = (bool)systemSummary["MeetsIntegrityTarget"];

            var status = meetsTarget ? 
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy :
                overallScore >= 0.7 ? 
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded :
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy;

            var description = $"Framework integrity score: {overallScore:P1} " +
                            $"(Target: 90%+, Status: {(meetsTarget ? "✓" : "✗")})";

            return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
                status, description, data: systemSummary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Framework integrity health check failed");
            return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy,
                $"Health check failed: {ex.Message}");
        }
    }
}

/// <summary>
/// Module compilation health check
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class ModuleCompilationHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly ILogger<ModuleCompilationHealthCheck> _logger;

    /// <summary>
    /// Initializes a new instance of the ModuleCompilationHealthCheck class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public ModuleCompilationHealthCheck(ILogger<ModuleCompilationHealthCheck> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Performs module compilation health check
    /// </summary>
    /// <param name="context">Health check context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    [TestableMethod("CheckHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 30000)]
    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Quick compilation check for core modules
            var coreModules = new[] { "Core", "Abstractions", "TestFramework" };
            var compilationResults = new Dictionary<string, bool>();

            foreach (var module in coreModules)
            {
                var projectPath = Path.Combine("modules", "src", module, $"ArtDesignFramework.{module}.csproj");
                if (File.Exists(projectPath))
                {
                    var buildResult = await QuickCompilationCheckAsync(projectPath, cancellationToken);
                    compilationResults[module] = buildResult;
                }
            }

            var successfulBuilds = compilationResults.Values.Count(r => r);
            var totalBuilds = compilationResults.Count;

            var status = successfulBuilds == totalBuilds ?
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy :
                successfulBuilds >= totalBuilds * 0.8 ?
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded :
                    Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy;

            var description = $"Module compilation: {successfulBuilds}/{totalBuilds} modules compile successfully";

            return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
                status, description, data: compilationResults.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Module compilation health check failed");
            return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
                Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy,
                $"Compilation check failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Performs quick compilation check
    /// </summary>
    /// <param name="projectPath">Project file path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if compilation succeeds</returns>
    private async Task<bool> QuickCompilationCheckAsync(string projectPath, CancellationToken cancellationToken)
    {
        try
        {
            var process = new System.Diagnostics.Process
            {
                StartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"build \"{projectPath}\" --no-restore --verbosity quiet",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            await process.WaitForExitAsync(cancellationToken);
            return process.ExitCode == 0;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Dependency validation health check
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class DependencyValidationHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    /// <summary>
    /// Performs dependency validation health check
    /// </summary>
    /// <param name="context">Health check context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    [TestableMethod("CheckHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Make async for interface compliance

        // Check if dependency validation tools exist
        var validationScript = Path.Combine("tools", "ValidateDependencies.ps1");
        var circularDetectionScript = Path.Combine("tools", "CircularDependencyDetector.ps1");

        var toolsExist = File.Exists(validationScript) && File.Exists(circularDetectionScript);
        var status = toolsExist ? 
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy :
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded;

        var description = toolsExist ? 
            "Dependency validation tools are available" :
            "Some dependency validation tools are missing";

        return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
            status, description, data: new Dictionary<string, object>
            {
                ["ValidationScriptExists"] = File.Exists(validationScript),
                ["CircularDetectionScriptExists"] = File.Exists(circularDetectionScript)
            });
    }
}

/// <summary>
/// Architectural compliance health check
/// Last Updated: 2025-01-27 22:55:00 UTC
/// </summary>
public class ArchitecturalComplianceHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    /// <summary>
    /// Performs architectural compliance health check
    /// </summary>
    /// <param name="context">Health check context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    [TestableMethod("CheckHealthAsync", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 5000)]
    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Make async for interface compliance

        // Check if architectural compliance analyzers exist
        var analyzerPath = Path.Combine("modules", "analyzers", "ArtDesignFramework.Analyzers");
        var analyzerExists = Directory.Exists(analyzerPath);

        var status = analyzerExists ? 
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy :
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded;

        var description = analyzerExists ? 
            "Architectural compliance analyzers are available" :
            "Architectural compliance analyzers not found";

        return new Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult(
            status, description, data: new Dictionary<string, object>
            {
                ["AnalyzerExists"] = analyzerExists,
                ["AnalyzerPath"] = analyzerPath
            });
    }
}

// Placeholder interfaces for enterprise services
public interface IHealthDashboardService { }
public interface IHealthReportingService { }
public interface IHealthAlertingService { }

// Placeholder implementations
public class HealthDashboardService : IHealthDashboardService { }
public class HealthReportingService : IHealthReportingService { }
public class HealthAlertingService : IHealthAlertingService { }
